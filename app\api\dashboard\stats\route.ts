import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { getReportFilter, checkAccess, getAccessibleReportTypes } from "@/lib/rbac"
import { UserRole } from "@prisma/client"

/**
 * Handler untuk mengambil statistik dashboard (GET /api/dashboard/stats)
 * @param request NextRequest - request dari client
 * @returns NextResponse - statistik laporan dan data ringkasan
 * @example
 * // Response sukses
 * {
 *   success: true,
 *   data: { ... },
 *   message: "Statistik dashboard berhasil diambil",
 *   error: null
 * }
 * // Response gagal
 * {
 *   success: false,
 *   data: null,
 *   message: "Unauthorized",
 *   error: "UNAUTHORIZED"
 * }
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || !session.user) {
      return NextResponse.json(
        { success: false, data: null, message: "Unauthorized", error: "UNAUTHORIZED" },
        { status: 401 }
      )
    }

    const userRole = session.user.role as UserRole

    // Get RBAC filter for user role
    const rbacFilter = getReportFilter(userRole)

    // If user has no access to any reports, return empty stats
    if (rbacFilter.id === "INVALID") {
      return NextResponse.json({
        success: true,
        data: {
          totalLaporan: 0,
          laporanBulanIni: 0,
          laporanMingguIni: 0,
          laporanHariIni: 0,
          jenisStats: { WBS: 0, COI: 0, PENGADUAN: 0 },
          statusStats: { BARU: 0, DIPROSES: 0, SELESAI: 0 },
          recentLaporan: [],
          userRole,
          accessibleTypes: []
        },
        message: "Statistik dashboard berhasil diambil",
        error: null
      })
    }

    // Get total laporan by jenis with RBAC filter
    const totalByJenis = await prisma.laporan.groupBy({
      by: ['jenis'],
      where: rbacFilter,
      _count: {
        id: true,
      },
    })

    // Get total laporan by status with RBAC filter
    const totalByStatus = await prisma.laporan.groupBy({
      by: ['status'],
      where: rbacFilter,
      _count: {
        id: true,
      },
    })

    // Get total laporan keseluruhan with RBAC filter
    const totalLaporan = await prisma.laporan.count({
      where: rbacFilter
    })

    // Get laporan bulan ini with RBAC filter
    const currentMonth = new Date()
    currentMonth.setDate(1)
    currentMonth.setHours(0, 0, 0, 0)

    const laporanBulanIni = await prisma.laporan.count({
      where: {
        ...rbacFilter,
        createdAt: {
          gte: currentMonth,
        },
      },
    })

    // Get laporan minggu ini with RBAC filter
    const currentWeek = new Date()
    const dayOfWeek = currentWeek.getDay()
    const diff = currentWeek.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1)
    currentWeek.setDate(diff)
    currentWeek.setHours(0, 0, 0, 0)

    const laporanMingguIni = await prisma.laporan.count({
      where: {
        ...rbacFilter,
        createdAt: {
          gte: currentWeek,
        },
      },
    })

    // Get laporan hari ini with RBAC filter
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    const laporanHariIni = await prisma.laporan.count({
      where: {
        ...rbacFilter,
        createdAt: {
          gte: today,
        },
      },
    })

    // Check if user can view details
    const canViewDetail = checkAccess(userRole, "WBS", "viewDetail") ||
                         checkAccess(userRole, "COI", "viewDetail") ||
                         checkAccess(userRole, "PENGADUAN", "viewDetail")

    // Get recent laporan with RBAC filter
    const recentLaporan = await prisma.laporan.findMany({
      where: rbacFilter,
      take: 5,
      orderBy: {
        createdAt: 'desc',
      },
      select: {
        id: true,
        jenis: true,
        status: true,
        createdAt: true,
        kronologi: canViewDetail, // Only include kronologi if user can view details
        lokasi: true,
      },
    })

    // Format data untuk chart
    const jenisStats = totalByJenis.reduce((acc, item) => {
      acc[item.jenis] = item._count.id
      return acc
    }, {} as Record<string, number>)

    const statusStats = totalByStatus.reduce((acc, item) => {
      acc[item.status] = item._count.id
      return acc
    }, {} as Record<string, number>)

    // Get accessible report types for this user
    const accessibleTypes = getAccessibleReportTypes(userRole, "viewList")

    // Ensure all jenis and status have values, but only show accessible ones
    const allJenis = ['WBS', 'COI', 'PENGADUAN']
    const allStatus = ['BARU', 'DIPROSES', 'SELESAI']

    // Initialize all jenis stats (will be 0 for non-accessible types)
    allJenis.forEach(jenis => {
      if (!jenisStats[jenis]) jenisStats[jenis] = 0
    })

    allStatus.forEach(status => {
      if (!statusStats[status]) statusStats[status] = 0
    })

    const responseData = {
      totalLaporan,
      laporanBulanIni,
      laporanMingguIni,
      laporanHariIni,
      jenisStats,
      statusStats,
      recentLaporan: recentLaporan.map(laporan => ({
        ...laporan,
        kronologi: canViewDetail && laporan.kronologi
          ? laporan.kronologi.substring(0, 100) + (laporan.kronologi.length > 100 ? '...' : '')
          : "[Detail hanya dapat dilihat oleh pengguna yang berwenang]",
      })),
      userRole,
      accessibleTypes,
      userPermissions: {
        canViewWBSDetail: checkAccess(userRole, "WBS", "viewDetail"),
        canViewCOIDetail: checkAccess(userRole, "COI", "viewDetail"),
        canViewPMDetail: checkAccess(userRole, "PENGADUAN", "viewDetail"),
      }
    }

    return NextResponse.json({
      success: true,
      data: responseData,
      message: "Statistik dashboard berhasil diambil",
      error: null
    })

  } catch (error) {
    console.error("[GET /api/dashboard/stats]", error)
    return NextResponse.json(
      { success: false, data: null, message: "Terjadi kesalahan server", error: "INTERNAL_SERVER_ERROR" },
      { status: 500 }
    )
  }
}
