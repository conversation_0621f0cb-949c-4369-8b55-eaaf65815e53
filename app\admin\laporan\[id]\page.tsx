"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { toast } from "sonner"
import { ArrowLeft, Calendar, User, MapPin, FileText, Download, Edit, Shield, EyeOff, Lock, AlertTriangle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import Link from "next/link"
import { checkAccess, ReportType, getRoleDisplayName } from "@/lib/rbac"
import { User<PERSON><PERSON> } from "@prisma/client"

interface Laporan {
  id: string // Changed from number to string for UUID
  jenis: string
  status: string
  createdAt: string
  updatedAt: string
  kronologi: string
  buktiUrl?: string
  kategori?: string
  pejabatDilaporkan?: string
  jenisBenturan?: string
  jenisPengaduan?: string
  lokasi?: string
  kontakPelapor?: {
    email?: string
    noHp?: string
  }
  limitedAccess?: boolean
  userPermissions?: {
    canView: boolean
    canViewDetail: boolean
    canUpdate: boolean
    canDelete: boolean
  }
}

export default function LaporanDetailPage() {
  const { data: session } = useSession()
  const params = useParams()
  const router = useRouter()
  const [laporan, setLaporan] = useState<Laporan | null>(null)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)
  const [newStatus, setNewStatus] = useState("")
  const [showUpdateDialog, setShowUpdateDialog] = useState(false)
  const [accessError, setAccessError] = useState<string | null>(null)
  const [hasDetailAccess, setHasDetailAccess] = useState(false)

  // Check if user is admin
  const isAdmin = session?.user?.role === "ADMIN"

  useEffect(() => {
    fetchLaporan()
  }, [])

  const fetchLaporan = async () => {
    if (!session?.user?.role) return

    setLoading(true)
    setAccessError(null)

    try {
      const id = Array.isArray(params.id) ? params.id[0] : params.id
      const response = await fetch(`/api/admin/laporan/${id}`)

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          const data = result.data
          setLaporan(data)
          setNewStatus(data.status)
          setHasDetailAccess(!data.limitedAccess)

          // Check if user has limited access
          if (data.limitedAccess) {
            setAccessError("You can only view basic information for this report type. Detail access is restricted for your role.")
          }
        } else {
          setAccessError(result.message || "Failed to load report")
        }
      } else if (response.status === 403) {
        setAccessError("Access denied. You don't have permission to view this report.")
      } else if (response.status === 404) {
        setAccessError("Report not found.")
        setTimeout(() => router.push("/admin/laporan"), 2000)
      } else {
        setAccessError("Failed to load report. Please try again.")
      }
    } catch (error) {
      console.error("Error fetching laporan:", error)
      setAccessError("An error occurred while loading the report.")
    } finally {
      setLoading(false)
    }
  }

  const handleUpdateStatus = async () => {
    if (!laporan || newStatus === laporan.status) {
      setShowUpdateDialog(false)
      return
    }

    setUpdating(true)
    try {
      const response = await fetch(`/api/laporan/${laporan.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (response.ok) {
        const result = await response.json()
        setLaporan(result.data)
        toast.success("Status laporan berhasil diupdate")
        setShowUpdateDialog(false)
      } else {
        toast.error("Gagal mengupdate status laporan")
      }
    } catch (error) {
      console.error("Error updating status:", error)
      toast.error("Terjadi kesalahan saat mengupdate status")
    } finally {
      setUpdating(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "BARU":
        return <Badge variant="destructive">Baru</Badge>
      case "DIPROSES":
        return <Badge variant="default">Diproses</Badge>
      case "SELESAI":
        return <Badge variant="secondary">Selesai</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getJenisBadge = (jenis: string) => {
    switch (jenis) {
      case "WBS":
        return <Badge className="bg-blue-100 text-blue-800">WBS</Badge>
      case "COI":
        return <Badge className="bg-orange-100 text-orange-800">COI</Badge>
      case "PENGADUAN":
        return <Badge className="bg-green-100 text-green-800">Pengaduan</Badge>
      default:
        return <Badge variant="outline">{jenis}</Badge>
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  // Show access error
  if (accessError) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link href="/admin/laporan">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Kembali
            </Button>
          </Link>
        </div>
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{accessError}</AlertDescription>
        </Alert>
        {session?.user?.role && (
          <div className="text-sm text-gray-600">
            Role Anda: {getRoleDisplayName(session.user.role as UserRole)}
          </div>
        )}
      </div>
    )
  }

  if (!laporan) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Laporan tidak ditemukan</p>
        <Link href="/admin/laporan">
          <Button className="mt-4">Kembali ke Daftar Laporan</Button>
        </Link>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Link href="/admin/laporan" className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-2">
            <ArrowLeft className="h-4 w-4 mr-1" />
            Kembali ke Daftar Laporan
          </Link>
          <h1 className="text-2xl font-bold text-gray-900">Detail Laporan #{laporan.id}</h1>
          <div className="flex items-center gap-2 mt-2">
            {getJenisBadge(laporan.jenis)}
            {getStatusBadge(laporan.status)}
          </div>
        </div>

        {/* Limited Access Warning */}
        {laporan.limitedAccess && (
          <Alert className="border-orange-200 bg-orange-50">
            <Lock className="h-4 w-4" />
            <AlertDescription>
              <strong>Akses Terbatas:</strong> Anda hanya dapat melihat informasi dasar untuk jenis laporan ini.
              Detail lengkap tidak tersedia untuk role {session?.user?.role ? getRoleDisplayName(session.user.role as UserRole) : 'Anda'}.
            </AlertDescription>
          </Alert>
        )}

        {/* Update Status Button - Only show if user has update permission */}
        {laporan.userPermissions?.canUpdate && (
          <Dialog open={showUpdateDialog} onOpenChange={setShowUpdateDialog}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Edit className="h-4 w-4" />
                Update Status
              </Button>
            </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Update Status Laporan</DialogTitle>
              <DialogDescription>
                Pilih status baru untuk laporan #{laporan.id}
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <Select value={newStatus} onValueChange={setNewStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Pilih status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="BARU">Baru</SelectItem>
                  <SelectItem value="DIPROSES">Diproses</SelectItem>
                  <SelectItem value="SELESAI">Selesai</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowUpdateDialog(false)}>
                Batal
              </Button>
              <Button onClick={handleUpdateStatus} disabled={updating}>
                {updating ? "Mengupdate..." : "Update Status"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Kronologi */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Kronologi Kejadian
                {!isAdmin && (
                  <Badge variant="outline" className="ml-2">
                    <Shield className="h-3 w-3 mr-1" />
                    Terbatas
                  </Badge>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isAdmin ? (
                <div className="prose max-w-none">
                  <p className="text-gray-700 whitespace-pre-wrap">{laporan.kronologi}</p>
                </div>
              ) : (
                <div className="flex items-center gap-3 p-6 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                  <EyeOff className="h-8 w-8 text-gray-400" />
                  <div>
                    <p className="font-medium text-gray-700">Detail Kronologi Terbatas</p>
                    <p className="text-sm text-gray-500">
                      Hanya Admin yang dapat melihat detail kronologi kejadian untuk menjaga kerahasiaan laporan.
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Bukti */}
          {laporan.buktiUrl && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Download className="h-5 w-5" />
                  Bukti Pendukung
                  {!isAdmin && (
                    <Badge variant="outline" className="ml-2">
                      <Shield className="h-3 w-3 mr-1" />
                      Terbatas
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isAdmin ? (
                  <div className="flex items-center gap-4">
                    <div className="flex-1">
                      <p className="text-sm text-gray-600">File bukti tersedia</p>
                      <p className="text-xs text-gray-400">{laporan.buktiUrl}</p>
                    </div>
                    <Button variant="outline" size="sm" asChild>
                      <a href={`/api${laporan.buktiUrl}`} target="_blank" rel="noopener noreferrer">
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </a>
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center gap-3 p-6 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                    <EyeOff className="h-8 w-8 text-gray-400" />
                    <div>
                      <p className="font-medium text-gray-700">Akses Bukti Terbatas</p>
                      <p className="text-sm text-gray-500">
                        Hanya Admin yang dapat mengakses dan mengunduh bukti pendukung untuk menjaga kerahasiaan.
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Info Laporan */}
          <Card>
            <CardHeader>
              <CardTitle>Informasi Laporan</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm font-medium">Tanggal Dibuat</p>
                  <p className="text-sm text-gray-600">
                    {new Date(laporan.createdAt).toLocaleDateString('id-ID', {
                      day: 'numeric',
                      month: 'long',
                      year: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm font-medium">Terakhir Diupdate</p>
                  <p className="text-sm text-gray-600">
                    {new Date(laporan.updatedAt).toLocaleDateString('id-ID', {
                      day: 'numeric',
                      month: 'long',
                      year: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
              </div>

              {/* Detail berdasarkan jenis */}
              {laporan.kategori && (
                <div>
                  <p className="text-sm font-medium">Kategori</p>
                  <p className="text-sm text-gray-600">{laporan.kategori}</p>
                </div>
              )}

              {laporan.pejabatDilaporkan && (
                <div>
                  <p className="text-sm font-medium">Pejabat yang Dilaporkan</p>
                  <p className="text-sm text-gray-600">{laporan.pejabatDilaporkan}</p>
                </div>
              )}

              {laporan.jenisBenturan && (
                <div>
                  <p className="text-sm font-medium">Jenis Benturan</p>
                  <p className="text-sm text-gray-600">{laporan.jenisBenturan}</p>
                </div>
              )}

              {laporan.jenisPengaduan && (
                <div>
                  <p className="text-sm font-medium">Jenis Pengaduan</p>
                  <p className="text-sm text-gray-600">{laporan.jenisPengaduan}</p>
                </div>
              )}

              {laporan.lokasi && (
                <div className="flex items-start gap-2">
                  <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium">Lokasi</p>
                    <p className="text-sm text-gray-600">{laporan.lokasi}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Kontak Pelapor */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Kontak Pelapor
              </CardTitle>
            </CardHeader>
            <CardContent>
              {laporan.kontakPelapor ? (
                <div className="space-y-2">
                  {laporan.kontakPelapor.email && (
                    <div>
                      <p className="text-sm font-medium">Email</p>
                      <p className="text-sm text-gray-600">{laporan.kontakPelapor.email}</p>
                    </div>
                  )}
                  {laporan.kontakPelapor.noHp && (
                    <div>
                      <p className="text-sm font-medium">No. HP</p>
                      <p className="text-sm text-gray-600">{laporan.kontakPelapor.noHp}</p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-sm text-gray-500">Laporan anonim</p>
                  <p className="text-xs text-gray-400">Tidak ada kontak pelapor</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
