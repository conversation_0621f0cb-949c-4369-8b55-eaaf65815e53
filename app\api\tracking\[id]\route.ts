import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { getSecurityHeaders } from "@/lib/sanitize"
import { isValidUUID6 } from "@/lib/uuid-generator"

/**
 * Handler untuk tracking status laporan publik (GET /api/tracking/[id])
 * @param request NextRequest - request dari client
 * @param params Object - parameter route, berisi id laporan
 * @returns NextResponse - data status laporan publik atau error
 * @example
 * // Response sukses
 * {
 *   success: true,
 *   data: { ...publicLaporan },
 *   message: "Status laporan berhasil diambil",
 *   error: null
 * }
 * // Response gagal
 * {
 *   success: false,
 *   data: null,
 *   message: "Laporan tidak ditemukan",
 *   error: "NOT_FOUND"
 * }
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const securityHeaders = getSecurityHeaders()
    const { id: idParam } = await params
    const id = idParam

    // Validate UUID format
    if (!isValidUUID6(id)) {
      return NextResponse.json(
        { success: false, data: null, message: "ID laporan tidak valid", error: "INVALID_ID" },
        { status: 400, headers: securityHeaders }
      )
    }

    // Get laporan with limited information for public tracking
    const laporan = await prisma.laporan.findUnique({
      where: { id },
      select: {
        id: true,
        jenis: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        // Include specific fields based on jenis but not sensitive data
        kategori: true,
        pejabatDilaporkan: true,
        jenisPengaduan: true,
        lokasi: true,
        // Check if contact exists without exposing actual contact info
        kontakPelapor: {
          select: {
            id: true, // Just to check if contact exists
          }
        }
      },
    })

    if (!laporan) {
      return NextResponse.json(
        { success: false, data: null, message: "Laporan tidak ditemukan", error: "NOT_FOUND" },
        { status: 404, headers: securityHeaders }
      )
    }

    // Transform data for public consumption
    const publicLaporan = {
      id: laporan.id,
      jenis: laporan.jenis,
      status: laporan.status,
      createdAt: laporan.createdAt.toISOString(),
      updatedAt: laporan.updatedAt.toISOString(),
      kategori: laporan.kategori,
      pejabatDilaporkan: laporan.pejabatDilaporkan,
      jenisPengaduan: laporan.jenisPengaduan,
      lokasi: laporan.lokasi,
      hasKontak: !!laporan.kontakPelapor, // Boolean to indicate if contact exists
    }

    return NextResponse.json({
      success: true,
      data: publicLaporan,
      message: "Status laporan berhasil diambil",
      error: null
    }, { headers: securityHeaders })

  } catch (error) {
    console.error("[GET /api/tracking/[id]]", error)
    return NextResponse.json(
      { success: false, data: null, message: "Terjadi kesalahan server", error: "INTERNAL_SERVER_ERROR" },
      { status: 500, headers: getSecurityHeaders() }
    )
  }
}
