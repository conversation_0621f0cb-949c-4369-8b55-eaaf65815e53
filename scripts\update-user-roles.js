const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function updateUserRoles() {
  try {
    console.log('🔄 Updating existing user roles...')

    // Get all users with old roles
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        role: true
      }
    })

    console.log(`Found ${users.length} users to check`)

    // Update users with ADMIN role to SUPER_ADMIN
    const adminUsers = users.filter(user => user.role === 'ADMIN')
    
    if (adminUsers.length > 0) {
      console.log(`Updating ${adminUsers.length} ADMIN users to SUPER_ADMIN`)
      
      for (const user of adminUsers) {
        await prisma.$executeRaw`
          UPDATE users 
          SET role = 'SUPER_ADMIN' 
          WHERE id = ${user.id}
        `
        console.log(`✅ Updated user ${user.email} from ADMIN to SUPER_ADMIN`)
      }
    }

    console.log('✅ User role update completed!')

  } catch (error) {
    console.error('❌ Error updating user roles:', error)
  } finally {
    await prisma.$disconnect()
  }
}

updateUserRoles()
