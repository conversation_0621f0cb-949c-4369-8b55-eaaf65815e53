const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

// Sample data untuk seeding
const laporanData = [
  // WBS Reports
  {
    jenis: 'WBS',
    status: 'BARU',
    kronologi: '<PERSON><PERSON><PERSON><PERSON> adanya praktik whistleblowing terkait penyalahgunaan anggaran proyek infrastruktur di Dinas Pekerjaan Umum. Pelapor menyampaikan bahwa terdapat markup harga material sebesar 30% dari harga pasar.',
    lokasi: 'Dinas <PERSON>, Jakarta Pusat',
    kategori: 'Penyalahgunaan Anggaran',
    pejabatDilaporkan: 'Kepala Dinas PU Jakarta Pusat',
    buktiUrl: '/uploads/bukti-wbs-001.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567890'
      }
    }
  },
  {
    jenis: 'WBS',
    status: 'DIPROSES',
    kronologi: 'Laporan mengenai dugaan korupsi dalam pengadaan alat kesehatan di RSUD. Terdapat indikasi kolusi antara panitia pengadaan dengan vendor tertentu yang mengakibatkan kerugian negara sekitar 2 miliar rupiah.',
    lokasi: 'RSUD Jakarta Timur',
    kategori: 'Korupsi Pengadaan',
    pejabatDilaporkan: 'Direktur RSUD Jakarta Timur',
    buktiUrl: '/uploads/bukti-wbs-002.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567891'
      }
    }
  },
  {
    jenis: 'WBS',
    status: 'SELESAI',
    kronologi: 'Pelaporan terkait penyalahgunaan kendaraan dinas untuk kepentingan pribadi oleh pejabat eselon II. Kendaraan dinas digunakan untuk keperluan keluarga dan bisnis pribadi secara rutin.',
    lokasi: 'Kantor Walikota Jakarta Selatan',
    kategori: 'Penyalahgunaan Fasilitas',
    pejabatDilaporkan: 'Sekretaris Daerah Jakarta Selatan',
    buktiUrl: '/uploads/bukti-wbs-003.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567892'
      }
    }
  },
  
  // COI Reports
  {
    jenis: 'COI',
    status: 'BARU',
    kronologi: 'Ditemukan konflik kepentingan dimana Kepala Bagian Pengadaan memiliki saham di perusahaan yang menjadi pemenang tender proyek senilai 5 miliar rupiah. Hal ini melanggar aturan conflict of interest.',
    lokasi: 'Dinas Pendidikan Jakarta Barat',
    jenisBenturan: 'Kepentingan Finansial',
    pejabatDilaporkan: 'Kepala Bagian Pengadaan Disdik Jakbar',
    buktiUrl: '/uploads/bukti-coi-001.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567893'
      }
    }
  },
  {
    jenis: 'COI',
    status: 'DIPROSES',
    kronologi: 'Konflik kepentingan dalam proses rekrutment dimana pejabat yang berwenang merekrut anggota keluarganya sendiri tanpa melalui proses seleksi yang transparan dan adil.',
    lokasi: 'Badan Kepegawaian Daerah Jakarta Utara',
    jenisBenturan: 'Kepentingan Keluarga',
    pejabatDilaporkan: 'Kepala BKD Jakarta Utara',
    buktiUrl: '/uploads/bukti-coi-002.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567894'
      }
    }
  },
  {
    jenis: 'COI',
    status: 'SELESAI',
    kronologi: 'Benturan kepentingan dalam pemberian izin usaha dimana pejabat pemberi izin memiliki hubungan bisnis dengan pemohon izin. Proses pemberian izin dilakukan dengan melewati prosedur standar.',
    lokasi: 'Dinas Penanaman Modal Jakarta Pusat',
    jenisBenturan: 'Kepentingan Bisnis',
    pejabatDilaporkan: 'Kepala Seksi Perizinan DPMPTSP',
    buktiUrl: '/uploads/bukti-coi-003.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567895'
      }
    }
  },
  
  // PENGADUAN Reports
  {
    jenis: 'PENGADUAN',
    status: 'BARU',
    kronologi: 'Masyarakat mengeluhkan pelayanan yang buruk di kantor kelurahan. Petugas sering tidak hadir, proses administrasi berbelit-belit, dan ada indikasi pungutan liar untuk mempercepat pengurusan dokumen.',
    lokasi: 'Kelurahan Menteng, Jakarta Pusat',
    jenisPengaduan: 'Pelayanan Publik',
    buktiUrl: '/uploads/bukti-pengaduan-001.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567896'
      }
    }
  },
  {
    jenis: 'PENGADUAN',
    status: 'DIPROSES',
    kronologi: 'Pengaduan mengenai diskriminasi dalam pelayanan kesehatan di puskesmas. Pasien dari kalangan ekonomi menengah ke bawah mendapat perlakuan yang berbeda dan harus menunggu lebih lama.',
    lokasi: 'Puskesmas Cempaka Putih, Jakarta Pusat',
    jenisPengaduan: 'Diskriminasi Pelayanan',
    buktiUrl: '/uploads/bukti-pengaduan-002.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567897'
      }
    }
  },
  {
    jenis: 'PENGADUAN',
    status: 'SELESAI',
    kronologi: 'Keluhan masyarakat terkait keterlambatan pembayaran bantuan sosial. Program bantuan yang seharusnya disalurkan setiap bulan mengalami keterlambatan hingga 3 bulan tanpa penjelasan yang jelas.',
    lokasi: 'Dinas Sosial Jakarta Timur',
    jenisPengaduan: 'Bantuan Sosial',
    buktiUrl: '/uploads/bukti-pengaduan-003.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567898'
      }
    }
  },
  
  // Additional reports for better testing
  {
    jenis: 'WBS',
    status: 'BARU',
    kronologi: 'Laporan dugaan suap dalam proses perizinan bangunan. Terdapat praktik pemberian uang pelicin kepada petugas untuk mempercepat proses penerbitan IMB dengan nilai sekitar 50 juta rupiah.',
    lokasi: 'Dinas Tata Ruang Jakarta Selatan',
    kategori: 'Suap dan Gratifikasi',
    pejabatDilaporkan: 'Kepala Seksi IMB Distaru Jaksel',
    buktiUrl: '/uploads/bukti-wbs-004.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567899'
      }
    }
  },
  {
    jenis: 'COI',
    status: 'BARU',
    kronologi: 'Konflik kepentingan dalam tender konsultan hukum dimana pejabat yang menentukan pemenang tender memiliki hubungan keluarga dengan pemilik firma hukum yang memenangkan tender tersebut.',
    lokasi: 'Sekretariat DPRD Jakarta',
    jenisBenturan: 'Kepentingan Keluarga',
    pejabatDilaporkan: 'Sekretaris DPRD Jakarta',
    buktiUrl: '/uploads/bukti-coi-004.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567800'
      }
    }
  },
  {
    jenis: 'PENGADUAN',
    status: 'DIPROSES',
    kronologi: 'Pengaduan terkait penyalahgunaan dana BOS di sekolah dasar. Dana yang seharusnya digunakan untuk fasilitas pembelajaran dialihkan untuk kepentingan pribadi kepala sekolah.',
    lokasi: 'SDN 01 Pagi Jakarta Barat',
    jenisPengaduan: 'Penyalahgunaan Dana',
    buktiUrl: '/uploads/bukti-pengaduan-004.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567801'
      }
    }
  }
]

async function seedLaporan() {
  try {
    console.log('🌱 Starting laporan seeding...')

    // Clear existing data
    console.log('🗑️  Clearing existing laporan data...')
    await prisma.kontakPelapor.deleteMany()
    await prisma.laporan.deleteMany()

    console.log('📝 Creating laporan records...')
    
    for (let i = 0; i < laporanData.length; i++) {
      const data = laporanData[i]
      
      const laporan = await prisma.laporan.create({
        data: {
          ...data,
          createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), // Random date within last 30 days
          updatedAt: new Date()
        },
        include: {
          kontakPelapor: true
        }
      })

      console.log(`✅ Created ${laporan.jenis} report #${laporan.id} - ${laporan.status}`)
    }

    // Get summary
    const summary = await prisma.laporan.groupBy({
      by: ['jenis', 'status'],
      _count: {
        id: true
      }
    })

    console.log('\n📊 Seeding Summary:')
    console.log('==================')
    
    const totalByJenis = {}
    const totalByStatus = {}
    let grandTotal = 0

    summary.forEach(item => {
      const key = `${item.jenis} - ${item.status}`
      console.log(`${key}: ${item._count.id} records`)
      
      totalByJenis[item.jenis] = (totalByJenis[item.jenis] || 0) + item._count.id
      totalByStatus[item.status] = (totalByStatus[item.status] || 0) + item._count.id
      grandTotal += item._count.id
    })

    console.log('\n📈 Total by Jenis:')
    Object.entries(totalByJenis).forEach(([jenis, count]) => {
      console.log(`  ${jenis}: ${count} records`)
    })

    console.log('\n📊 Total by Status:')
    Object.entries(totalByStatus).forEach(([status, count]) => {
      console.log(`  ${status}: ${count} records`)
    })

    console.log(`\n🎉 Total laporan created: ${grandTotal}`)
    console.log('✅ Laporan seeding completed successfully!')

  } catch (error) {
    console.error('❌ Error seeding laporan:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run seeder
if (require.main === module) {
  seedLaporan()
    .catch((error) => {
      console.error('❌ Seeding failed:', error)
      process.exit(1)
    })
}

module.exports = { seedLaporan }
