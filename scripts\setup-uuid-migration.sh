#!/bin/bash

# Setup UUID Migration Script
# This script will prepare the database for UUID migration

echo "🚀 Setting up UUID Migration..."

# 1. Backup current database
echo "📦 Creating database backup..."
timestamp=$(date +"%Y%m%d_%H%M%S")
backup_file="backup_before_uuid_migration_${timestamp}.sql"

# Create backup (adjust connection details as needed)
# mysqldump -u your_username -p your_database > $backup_file
echo "⚠️  Please create a manual backup of your database before proceeding!"
echo "   Example: mysqldump -u username -p database_name > $backup_file"

# 2. Generate new Prisma client
echo "🔧 Generating new Prisma client..."
npx prisma generate

# 3. Check if migration is needed
echo "🔍 Checking current database schema..."
npx prisma db pull --print

echo "📋 Migration setup completed!"
echo ""
echo "Next steps:"
echo "1. ✅ Backup your database manually"
echo "2. 🔧 Run: npm run migrate:uuid"
echo "3. 🧪 Test the application"
echo "4. 🚀 Deploy if everything works"
echo ""
echo "⚠️  WARNING: This migration will change all ID fields from integers to UUID strings!"
echo "   Make sure to backup your data before proceeding."
