# 🔄 UUID Migration Guide

Panduan lengkap untuk migrasi ID dari integer ke UUID 6 karakter pada tabel `laporan` dan `kontak_pelapor`.

## 📋 **Overview**

### **Sebelum Migration:**
- `laporan.id`: `INT AUTO_INCREMENT` (1, 2, 3, ...)
- `kontak_pelapor.id`: `INT AUTO_INCREMENT` (1, 2, 3, ...)
- `kontak_pelapor.laporanId`: `INT` (foreign key)

### **Setelah Migration:**
- `laporan.id`: `VARCHAR(6)` (A1B2C3, X9Y8Z7, ...)
- `kontak_pelapor.id`: `VARCHAR(6)` (M4N5P6, Q7R8S9, ...)
- `kontak_pelapor.laporanId`: `VARCHAR(6)` (foreign key)

## 🎯 **Keuntungan UUID 6 Karakter**

1. **User-Friendly**: Mudah diingat dan diketik (6 karakter vs 36 karakter UUID standar)
2. **Secure**: Tidak sequential, sulit ditebak
3. **Compact**: Hemat space di database dan UI
4. **Professional**: Terlihat lebih profesional untuk ID tracking
5. **Collision-Resistant**: 36^6 = 2.1 miliar kombinasi

## 🚀 **Langkah-langkah Migration**

### **1. Persiapan**

```bash
# 1. Backup database
mysqldump -u username -p database_name > backup_before_uuid.sql

# 2. Setup migration tools
npm run setup:uuid

# 3. Generate Prisma client baru
npm run db:generate
```

### **2. Jalankan Migration**

```bash
# Jalankan migration script
npm run migrate:uuid
```

### **3. Verifikasi**

```bash
# Check database
npm run db:studio

# Test aplikasi
npm run dev
```

## 🔧 **Technical Details**

### **UUID Generator Function**

```typescript
// Generate UUID 6 karakter: A1B2C3
export function generateUUID6(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}
```

### **Prisma Schema Changes**

```prisma
// BEFORE
model Laporan {
  id          Int           @id @default(autoincrement())
  // ...
}

model KontakPelapor {
  id        Int     @id @default(autoincrement())
  laporanId Int     @unique
  // ...
}

// AFTER
model Laporan {
  id          String        @id @db.VarChar(6)
  // ...
}

model KontakPelapor {
  id        String  @id @db.VarChar(6)
  laporanId String  @unique @db.VarChar(6)
  // ...
}
```

### **API Changes**

```typescript
// Generate UUID saat create laporan
const laporanId = await generateUniqueUUID6(async (uuid) => {
  const existing = await prisma.laporan.findUnique({ where: { id: uuid } })
  return !!existing
})

const laporan = await prisma.laporan.create({
  data: {
    id: laporanId,
    // ... other fields
  }
})
```

## 🎨 **UI Components**

### **UUID Display Component**

```tsx
import { UUIDDisplay } from '@/components/ui/uuid-display'

// Display UUID dengan format ABC-123
<UUIDDisplay 
  uuid="A1B2C3" 
  label="ID Laporan"
  showCopy={true}
/>
```

### **UUID Input Component**

```tsx
import { UUIDInput } from '@/components/ui/uuid-display'

// Input dengan validasi real-time
<UUIDInput
  value={uuid}
  onChange={setUuid}
  placeholder="Masukkan ID (6 karakter)"
/>
```

### **UUID Badge Component**

```tsx
import { UUIDBadge } from '@/components/ui/uuid-display'

// Badge dengan warna berbeda
<UUIDBadge 
  uuid="A1B2C3" 
  variant="success"
  size="md"
/>
```

## 📊 **Migration Script Details**

Migration script akan:

1. **Backup existing data** ke memory
2. **Create temporary tables** dengan UUID schema
3. **Generate unique UUIDs** untuk setiap record
4. **Migrate data** dengan mapping ID lama ke UUID baru
5. **Drop old tables** dan rename new tables
6. **Verify migration** success

## ⚠️ **Important Notes**

### **Breaking Changes**
- Semua API endpoint yang menggunakan ID akan berubah format
- Frontend components perlu update untuk handle UUID
- External integrations perlu update

### **Rollback Plan**
Jika migration gagal:
```bash
# Restore dari backup
mysql -u username -p database_name < backup_before_uuid.sql

# Revert Prisma schema
git checkout HEAD~1 -- prisma/schema.prisma

# Regenerate client
npm run db:generate
```

### **Testing Checklist**
- [ ] Form submission works (WBS, COI, Pengaduan)
- [ ] Success page shows formatted UUID
- [ ] Admin dashboard displays UUIDs correctly
- [ ] Search by ID works
- [ ] Email notifications include correct ID
- [ ] Export functions work
- [ ] API endpoints return correct format

## 🔍 **Troubleshooting**

### **Common Issues**

1. **"Type 'string' is not assignable to type 'number'"**
   - Solution: Regenerate Prisma client after schema changes

2. **"UUID format invalid"**
   - Solution: Check UUID validation in components

3. **"Foreign key constraint fails"**
   - Solution: Ensure UUID mapping is correct in migration

### **Debug Commands**

```bash
# Check current schema
npx prisma db pull --print

# Validate UUIDs in database
SELECT id, LENGTH(id), id REGEXP '^[A-Z0-9]{6}$' as valid FROM laporan;

# Count records before/after migration
SELECT COUNT(*) FROM laporan;
SELECT COUNT(*) FROM kontak_pelapor;
```

## 📞 **Support**

Jika mengalami masalah:
1. Check logs di console browser dan server
2. Verify database schema dengan `npm run db:studio`
3. Test dengan data minimal terlebih dahulu
4. Rollback jika diperlukan

## 🎉 **Post-Migration**

Setelah migration berhasil:
1. Update documentation
2. Inform users tentang format ID baru
3. Monitor system untuk issues
4. Update backup procedures
5. Clean up migration scripts jika tidak diperlukan lagi
