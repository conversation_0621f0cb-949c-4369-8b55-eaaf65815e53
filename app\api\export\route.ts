import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { exportToExcel } from "@/lib/export"
import { canExportAny, getExportFilter, checkAccess } from "@/lib/rbac"
import { UserRole } from "@prisma/client"
/**
 * Handler untuk mengekspor data laporan (GET /api/export)
 * @param request NextRequest - request dari client, bisa berisi query filter
 * @returns NextResponse - file excel atau data laporan dalam JSON
 * @example
 * // Response sukses (JSON)
 * {
 *   success: true,
 *   data: [...],
 *   message: "Data laporan berhasil diekspor",
 *   error: null,
 *   total: 10,
 *   filters: { jenis, status, startDate, endDate }
 * }
 * // Response gagal
 * {
 *   success: false,
 *   data: null,
 *   message: "Unauthorized",
 *   error: "UNAUTHORIZED"
 * }
 */
import { <PERSON><PERSON><PERSON>aporan, StatusLaporan } from "@prisma/client"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || !session.user) {
      return NextResponse.json(
        { success: false, data: null, message: "Unauthorized", error: "UNAUTHORIZED" },
        { status: 401 }
      )
    }

    // Check export permissions
    const userRole = session.user.role as UserRole
    if (!canExportAny(userRole)) {
      return NextResponse.json(
        { success: false, data: null, message: "Access denied - no export permissions", error: "FORBIDDEN" },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const format = searchParams.get("format") || "excel"
    const jenis = searchParams.get("jenis")
    const status = searchParams.get("status")
    const startDate = searchParams.get("startDate")
    const endDate = searchParams.get("endDate")

    // Get RBAC export filter
    const rbacFilter = getExportFilter(userRole)

    // Build where clause with RBAC filter
    const where: any = {
      ...rbacFilter
    }
    
    if (jenis && Object.values(JenisLaporan).includes(jenis as JenisLaporan)) {
      // Check if user can export this specific report type
      const jenisAsReportType = jenis === "PENGADUAN" ? "PENGADUAN" : jenis as "WBS" | "COI"
      if (!checkAccess(userRole, jenisAsReportType, "export")) {
        return NextResponse.json(
          { success: false, data: null, message: `Access denied - cannot export ${jenis} reports`, error: "FORBIDDEN" },
          { status: 403 }
        )
      }
      where.jenis = jenis as JenisLaporan
    }
    
    if (status && Object.values(StatusLaporan).includes(status as StatusLaporan)) {
      where.status = status as StatusLaporan
    }
    
    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) {
        where.createdAt.gte = new Date(startDate)
      }
      if (endDate) {
        const endDateTime = new Date(endDate)
        endDateTime.setHours(23, 59, 59, 999)
        where.createdAt.lte = endDateTime
      }
    }

    // Get laporan data
    const laporan = await prisma.laporan.findMany({
      where,
      include: {
        kontakPelapor: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    })

    // Transform data for export
    const exportData = laporan.map(item => ({
      id: item.id,
      jenis: item.jenis,
      status: item.status,
      createdAt: item.createdAt.toISOString(),
      kronologi: item.kronologi,
      kategori: item.kategori,
      pejabatDilaporkan: item.pejabatDilaporkan,
      jenisBenturan: item.jenisBenturan,
      jenisPengaduan: item.jenisPengaduan,
      lokasi: item.lokasi,
      kontakEmail: item.kontakPelapor?.email,
      kontakHp: item.kontakPelapor?.noHp,
    }))

    if (format === "excel") {
      const buffer = await exportToExcel(exportData)

      // Generate filename
      const date = new Date().toISOString().split('T')[0]
      let filename = `laporan_${date}`
      if (jenis) filename += `_${jenis.toLowerCase()}`
      if (status) filename += `_${status.toLowerCase()}`
      filename += '.xlsx'

      return new NextResponse(buffer as any, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'Content-Disposition': `attachment; filename="${filename}"`,
        },
      })
    }

    // For other formats or JSON response
    return NextResponse.json({
      success: true,
      data: exportData,
      message: "Data laporan berhasil diekspor",
      error: null,
      total: exportData.length,
      filters: {
        jenis,
        status,
        startDate,
        endDate,
      },
    })

  } catch (error) {
    console.error("[GET /api/export]", error)
    return NextResponse.json(
      { success: false, data: null, message: "Terjadi kesalahan server", error: "INTERNAL_SERVER_ERROR" },
      { status: 500 }
    )
  }
}
