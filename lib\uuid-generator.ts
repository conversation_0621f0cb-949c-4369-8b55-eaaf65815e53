/**
 * UUID Generator untuk ID Laporan dan Kontak Pelapor
 * Format: 6 karakter alphanumeric (huruf besar + angka)
 * Contoh: A1B2C3, X9Y8Z7, M4N5P6
 */

const CHARACTERS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'

/**
 * Generate UUID 6 karakter
 * @returns string - UUID 6 karakter (contoh: "A1B2C3")
 */
export function generateUUID6(): string {
  let result = ''
  for (let i = 0; i < 6; i++) {
    result += CHARACTERS.charAt(Math.floor(Math.random() * CHARACTERS.length))
  }
  return result
}

/**
 * Generate UUID 6 karakter yang unik (dengan pengecekan database)
 * @param checkExists - Function untuk cek apakah UUID sudah ada
 * @returns Promise<string> - UUID 6 karakter yang unik
 */
export async function generateUniqueUUID6(
  checkExists: (uuid: string) => Promise<boolean>
): Promise<string> {
  let uuid: string
  let attempts = 0
  const maxAttempts = 100 // Prevent infinite loop
  
  do {
    uuid = generateUUID6()
    attempts++
    
    if (attempts > maxAttempts) {
      throw new Error('Failed to generate unique UUID after maximum attempts')
    }
  } while (await checkExists(uuid))
  
  return uuid
}

/**
 * Validate UUID format (6 karakter alphanumeric)
 * @param uuid - UUID string to validate
 * @returns boolean - true if valid format
 */
export function isValidUUID6(uuid: string): boolean {
  if (!uuid || typeof uuid !== 'string') {
    return false
  }
  
  // Must be exactly 6 characters
  if (uuid.length !== 6) {
    return false
  }
  
  // Must contain only uppercase letters and numbers
  const validPattern = /^[A-Z0-9]{6}$/
  return validPattern.test(uuid)
}

/**
 * Format UUID untuk display (dengan separator)
 * @param uuid - UUID 6 karakter
 * @returns string - UUID dengan format display (contoh: "A1B-2C3")
 */
export function formatUUIDForDisplay(uuid: string): string {
  if (!isValidUUID6(uuid)) {
    return uuid // Return as-is if invalid
  }
  
  // Format: ABC-123
  return `${uuid.substring(0, 3)}-${uuid.substring(3, 6)}`
}

/**
 * Parse UUID dari format display
 * @param formattedUuid - UUID dengan separator (contoh: "A1B-2C3")
 * @returns string - UUID tanpa separator (contoh: "A1B2C3")
 */
export function parseUUIDFromDisplay(formattedUuid: string): string {
  if (!formattedUuid || typeof formattedUuid !== 'string') {
    return formattedUuid
  }
  
  // Remove separator
  return formattedUuid.replace('-', '')
}

/**
 * Generate multiple unique UUIDs
 * @param count - Number of UUIDs to generate
 * @param checkExists - Function untuk cek apakah UUID sudah ada
 * @returns Promise<string[]> - Array of unique UUIDs
 */
export async function generateMultipleUniqueUUID6(
  count: number,
  checkExists: (uuid: string) => Promise<boolean>
): Promise<string[]> {
  const uuids: string[] = []
  const generatedSet = new Set<string>()
  
  for (let i = 0; i < count; i++) {
    let uuid: string
    let attempts = 0
    const maxAttempts = 100
    
    do {
      uuid = generateUUID6()
      attempts++
      
      if (attempts > maxAttempts) {
        throw new Error(`Failed to generate unique UUID ${i + 1} after maximum attempts`)
      }
    } while (await checkExists(uuid) || generatedSet.has(uuid))
    
    uuids.push(uuid)
    generatedSet.add(uuid)
  }
  
  return uuids
}

// Export constants for external use
export const UUID_LENGTH = 6
export const UUID_CHARACTERS = CHARACTERS
export const UUID_PATTERN = /^[A-Z0-9]{6}$/
