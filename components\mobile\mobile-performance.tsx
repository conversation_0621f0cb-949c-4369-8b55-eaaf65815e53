"use client"

import { useEffect } from "react"

// Mobile performance optimizations
export function MobilePerformanceOptimizer() {
  useEffect(() => {
    // Prevent zoom on double tap
    let lastTouchEnd = 0
    const preventZoom = (e: TouchEvent) => {
      const now = new Date().getTime()
      if (now - lastTouchEnd <= 300) {
        e.preventDefault()
      }
      lastTouchEnd = now
    }
    
    document.addEventListener('touchend', preventZoom, { passive: false })
    
    // Optimize scroll performance
    const optimizeScroll = () => {
      // Type assertion for webkit-specific CSS property
      const bodyStyle = document.body.style as any
      bodyStyle.webkitOverflowScrolling = 'touch'
    }
    
    optimizeScroll()
    
    // Cleanup
    return () => {
      document.removeEventListener('touchend', preventZoom)
    }
  }, [])

  return null
}

// Lazy loading image component
interface LazyImageProps {
  src: string
  alt: string
  className?: string
  width?: number
  height?: number
  placeholder?: string
}

export function LazyImage({ 
  src, 
  alt, 
  className = "", 
  width, 
  height, 
  placeholder = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PC9zdmc+"
}: LazyImageProps) {
  return (
    <img
      src={src}
      alt={alt}
      className={`transition-opacity duration-300 ${className}`}
      width={width}
      height={height}
      loading="lazy"
      decoding="async"
      style={{
        backgroundImage: `url(${placeholder})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
      }}
    />
  )
}

// Mobile-optimized skeleton loader
export function MobileSkeleton({ 
  className = "",
  lines = 1,
  height = "h-4"
}: {
  className?: string
  lines?: number
  height?: string
}) {
  return (
    <div className={`animate-pulse ${className}`}>
      {Array.from({ length: lines }).map((_, i) => (
        <div
          key={i}
          className={`bg-gray-200 rounded ${height} ${i > 0 ? 'mt-2' : ''}`}
        />
      ))}
    </div>
  )
}

// Mobile loading spinner
export function MobileSpinner({ 
  size = "w-6 h-6",
  className = ""
}: {
  size?: string
  className?: string
}) {
  return (
    <div className={`${size} ${className}`}>
      <div className="animate-spin rounded-full border-2 border-gray-300 border-t-primary h-full w-full" />
    </div>
  )
}

// Mobile-optimized error boundary
interface MobileErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function MobileErrorBoundary({ 
  children, 
  fallback 
}: MobileErrorBoundaryProps) {
  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      {fallback || (
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Terjadi Kesalahan</h2>
            <p className="text-gray-600 mt-1">Silakan refresh halaman atau coba lagi nanti</p>
          </div>
          <button
            onClick={() => window.location.reload()}
            className="bg-primary text-white px-6 py-3 rounded-lg font-medium touch-manipulation active:scale-95 transition-transform"
          >
            Refresh Halaman
          </button>
        </div>
      )}
    </div>
  )
}

// Mobile network status indicator
export function MobileNetworkStatus() {
  useEffect(() => {
    const handleOnline = () => {
      // Show online status
      const toast = document.createElement('div')
      toast.className = 'fixed top-4 left-4 right-4 bg-green-500 text-white p-3 rounded-lg z-50 text-center'
      toast.textContent = '✅ Koneksi internet tersambung'
      document.body.appendChild(toast)
      
      setTimeout(() => {
        document.body.removeChild(toast)
      }, 3000)
    }

    const handleOffline = () => {
      // Show offline status
      const toast = document.createElement('div')
      toast.className = 'fixed top-4 left-4 right-4 bg-red-500 text-white p-3 rounded-lg z-50 text-center'
      toast.textContent = '❌ Koneksi internet terputus'
      document.body.appendChild(toast)
      
      setTimeout(() => {
        if (document.body.contains(toast)) {
          document.body.removeChild(toast)
        }
      }, 5000)
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  return null
}

// Mobile viewport height fix for iOS
export function MobileViewportFix() {
  useEffect(() => {
    const setVH = () => {
      const vh = window.innerHeight * 0.01
      document.documentElement.style.setProperty('--vh', `${vh}px`)
    }

    setVH()
    window.addEventListener('resize', setVH)
    window.addEventListener('orientationchange', setVH)

    return () => {
      window.removeEventListener('resize', setVH)
      window.removeEventListener('orientationchange', setVH)
    }
  }, [])

  return null
}

// Mobile-optimized intersection observer hook
export function useMobileIntersectionObserver(
  elementRef: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) {
  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          // Element is visible
          element.classList.add('animate-fade-in')
        }
      })
    }, {
      threshold: 0.1,
      rootMargin: '50px',
      ...options
    })

    observer.observe(element)

    return () => {
      observer.unobserve(element)
    }
  }, [elementRef, options])
}

// Mobile performance metrics (disabled)
export function MobilePerformanceMetrics() {
  // Performance metrics logging disabled to reduce console noise
  // Uncomment below code if you need to debug performance issues

  /*
  useEffect(() => {
    // Log performance metrics for mobile
    if (typeof window !== 'undefined' && 'performance' in window) {
      const logMetrics = () => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming

        // Mobile Performance Metrics (only for debugging)
        console.log('Mobile Performance Metrics:', {
          'DNS Lookup': navigation.domainLookupEnd - navigation.domainLookupStart,
          'TCP Connection': navigation.connectEnd - navigation.connectStart,
          'Request': navigation.responseStart - navigation.requestStart,
          'Response': navigation.responseEnd - navigation.responseStart,
          'DOM Processing': navigation.domContentLoadedEventStart - navigation.responseEnd,
          'Total Load Time': navigation.loadEventEnd - navigation.fetchStart,
        })
      }

      if (document.readyState === 'complete') {
        logMetrics()
      } else {
        window.addEventListener('load', logMetrics)
      }
    }
  }, [])
  */

  return null
}
