"use client"

import { useSession } from "next-auth/react"
import { useRouter, usePathname } from "next/navigation"
import { useEffect } from "react"
import { AdminSidebar } from "@/components/layout/admin-sidebar"
import { AdminMobileHeader } from "@/components/layout/admin-mobile-header"
import { LoadingPage } from "@/components/ui/loading"

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    if (status === "loading") return // Still loading

    if (!session && pathname !== "/admin/login") {
      router.push("/admin/login")
      return
    }

    // Remove automatic redirect from login page to avoid conflicts
    // Let the login page handle its own redirect
  }, [session, status, router, pathname])

  if (status === "loading") {
    return <LoadingPage />
  }

  if (!session && pathname !== "/admin/login") {
    return <LoadingPage />
  }

  // <PERSON><PERSON> di halaman login, tampilkan tanpa layout admin
  if (pathname === "/admin/login") {
    return <>{children}</>
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Header - only shows on mobile */}
      <AdminMobileHeader />

      <div className="flex h-screen">
        <AdminSidebar />
        <main className="flex-1 p-4 md:p-6 pt-16 md:pt-6 overflow-y-auto">
          {children}
        </main>
      </div>
    </div>
  )
}
