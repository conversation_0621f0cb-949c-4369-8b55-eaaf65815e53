# 📧 Email System UUID Migration Fixes

Dokumentasi perbaikan email system untuk kompatibilitas dengan UUID string format.

## ❌ **Original Error**

```
./app/api/laporan/[id]/route.ts:193:11
Type error: Argument of type 'string' is not assignable to parameter of type 'number'.

  191 |         await sendStatusUpdateNotification(
  192 |           updatedLaporan.kontakPelapor.email,
> 193 |           updatedLaporan.id,
      |           ^
  194 |           updatedLaporan.jenis,
  195 |           existingLaporan.status,
  196 |           status
```

## 🎯 **Root Cause**

Email functions di `lib/email.ts` masih menggunakan parameter `laporanId: number` sedangkan database sekarang menggunakan UUID string format.

## ✅ **Files Fixed**

### **lib/email.ts - Email Functions**

#### **1. generateLaporanConfirmationEmail Function:**

**Before (Problematic):**
```typescript
export function generateLaporanConfirmationEmail(
  laporanId: number, // ❌ Still using number
  jenis: string
): { subject: string; html: string; text: string }
```

**After (Fixed):**
```typescript
export function generateLaporanConfirmationEmail(
  laporanId: string, // ✅ Changed to string for UUID
  jenis: string
): { subject: string; html: string; text: string }
```

#### **2. generateStatusUpdateEmail Function:**

**Before (Problematic):**
```typescript
export function generateStatusUpdateEmail(
  laporanId: number, // ❌ Still using number
  jenis: string,
  oldStatus: string,
  newStatus: string
): { subject: string; html: string; text: string }
```

**After (Fixed):**
```typescript
export function generateStatusUpdateEmail(
  laporanId: string, // ✅ Changed to string for UUID
  jenis: string,
  oldStatus: string,
  newStatus: string
): { subject: string; html: string; text: string }
```

#### **3. sendLaporanConfirmation Function:**

**Before (Problematic):**
```typescript
export async function sendLaporanConfirmation(
  email: string,
  laporanId: number, // ❌ Still using number
  jenis: string
): Promise<boolean>
```

**After (Fixed):**
```typescript
export async function sendLaporanConfirmation(
  email: string,
  laporanId: string, // ✅ Changed to string for UUID
  jenis: string
): Promise<boolean>
```

#### **4. sendStatusUpdateNotification Function:**

**Before (Problematic):**
```typescript
export async function sendStatusUpdateNotification(
  email: string,
  laporanId: number, // ❌ Still using number
  jenis: string,
  oldStatus: string,
  newStatus: string
): Promise<boolean>
```

**After (Fixed):**
```typescript
export async function sendStatusUpdateNotification(
  email: string,
  laporanId: string, // ✅ Changed to string for UUID
  jenis: string,
  oldStatus: string,
  newStatus: string
): Promise<boolean>
```

## 📧 **Email Templates Updated**

### **Email Content with UUID:**

#### **Confirmation Email:**
```html
<h2>Laporan WBS Berhasil Diterima</h2>
<div class="tracking-id">
  <h3>ID Tracking Laporan Anda:</h3>
  <code>A1B2C3</code> <!-- ✅ Now shows UUID instead of number -->
  <p><strong>Penting:</strong> Simpan ID ini untuk melacak status laporan Anda.</p>
</div>
```

#### **Status Update Email:**
```html
<h2>Update Status Laporan WBS #A1B2C3</h2> <!-- ✅ UUID in subject -->
<div class="status-update">
  <h3>Perubahan Status:</h3>
  <p><strong>Dari:</strong> Baru</p>
  <p><strong>Ke:</strong> Sedang Diproses</p>
</div>
```

### **Email Links with UUID:**
```html
<a href="https://wbs.example.com/tracking?id=A1B2C3" class="button">
  Tracking Laporan
</a>
```

## 🔧 **API Integration**

### **Laporan Creation (POST /api/laporan):**
```typescript
// app/api/laporan/route.ts
if (kontakEmail && kontakEmail.trim() !== '') {
  try {
    await sendLaporanConfirmation(
      kontakEmail, 
      laporan.id, // ✅ UUID string from database
      jenis
    )
  } catch (error) {
    console.error('Failed to send confirmation email:', error)
  }
}
```

### **Status Update (PUT /api/laporan/[id]):**
```typescript
// app/api/laporan/[id]/route.ts
if (updatedLaporan.kontakPelapor?.email && existingLaporan.status !== status) {
  try {
    await sendStatusUpdateNotification(
      updatedLaporan.kontakPelapor.email,
      updatedLaporan.id, // ✅ UUID string from database
      updatedLaporan.jenis,
      existingLaporan.status,
      status
    )
  } catch (error) {
    console.error('Failed to send status update email:', error)
  }
}
```

## 📱 **Email Display Examples**

### **Subject Lines:**
- ✅ `Konfirmasi Laporan WBS #A1B2C3`
- ✅ `Update Status Laporan COI #X9Y8Z7`
- ✅ `Konfirmasi Laporan PENGADUAN #M4N5P6`

### **Email Body:**
```
Laporan WBS Berhasil Diterima

Terima kasih telah mengirimkan laporan melalui sistem WBS kami.

ID Tracking Laporan Anda: A1B2C3

Penting: Simpan ID ini untuk melacak status laporan Anda.

Langkah Selanjutnya:
1. Simpan ID tracking laporan: A1B2C3
2. Gunakan ID tersebut untuk memantau status laporan
3. Tim kami akan memproses laporan sesuai prosedur yang berlaku

Untuk tracking laporan, kunjungi: https://wbs.example.com/tracking?id=A1B2C3
```

## 🧪 **Testing Email System**

### **Test Cases:**

#### **1. Laporan Creation Email:**
```bash
POST /api/laporan
{
  "jenis": "WBS",
  "kronologi": "Test report",
  "kontakEmail": "<EMAIL>"
}

# Expected: Email sent with UUID in subject and body
# Subject: "Konfirmasi Laporan WBS #A1B2C3"
```

#### **2. Status Update Email:**
```bash
PUT /api/laporan/A1B2C3
{
  "status": "DIPROSES"
}

# Expected: Email sent with UUID in subject and body
# Subject: "Update Status Laporan WBS #A1B2C3"
```

### **Validation:**
- ✅ **Email subjects** contain UUID format (A1B2C3)
- ✅ **Email bodies** display UUID correctly
- ✅ **Tracking links** use UUID parameter
- ✅ **No TypeScript errors** in email functions

## 🔗 **Tracking System Integration**

### **Email Links:**
All email templates now generate tracking links with UUID format:
```
https://wbs.example.com/tracking?id=A1B2C3
```

### **Tracking Page Compatibility:**
The tracking page should handle UUID format:
```typescript
// app/tracking/page.tsx
const searchParams = useSearchParams()
const laporanId = searchParams.get('id') // Gets UUID string like "A1B2C3"

// Fetch laporan details using UUID
const response = await fetch(`/api/laporan/${laporanId}`)
```

## 🚀 **Build Status**

After these fixes:
- ✅ **TypeScript compilation**: No more parameter type errors
- ✅ **Email functionality**: All email functions accept UUID strings
- ✅ **Email templates**: Display UUID format correctly
- ✅ **API integration**: Seamless UUID passing to email functions
- ✅ **User experience**: Consistent UUID format across all communications

## 📋 **Migration Checklist**

- [x] **Email template functions** - Updated to accept string UUID
- [x] **Email sending functions** - Updated parameter types
- [x] **API integration** - Passes UUID strings correctly
- [x] **Email content** - Displays UUID format properly
- [x] **Tracking links** - Use UUID parameters
- [x] **TypeScript types** - All functions use string UUID

## 🎯 **Benefits of UUID in Emails**

### **User Experience:**
- ✅ **Shorter IDs**: 6 characters vs long numbers
- ✅ **Easy to remember**: Alphanumeric format
- ✅ **Easy to type**: Can be entered manually if needed

### **Security:**
- ✅ **Non-sequential**: Can't guess other report IDs
- ✅ **No enumeration**: Prevents systematic access attempts
- ✅ **Unique format**: Globally unique identifiers

### **Technical:**
- ✅ **Consistent format**: Same across all systems
- ✅ **Database efficiency**: Fixed-width string indexing
- ✅ **API compatibility**: Works with all endpoints

All email system components are now fully compatible with UUID string format! 📧🎉
