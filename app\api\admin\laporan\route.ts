import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { checkAccess, getReportFilter, ReportType } from "@/lib/rbac"
import { UserRole } from "@prisma/client"

/**
 * GET /api/admin/laporan - Get laporan list with RBAC filtering
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const userRole = session.user.role as UserRole
    const { searchParams } = new URL(request.url)

    // Get query parameters
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const jenis = searchParams.get("jenis") as ReportType | null
    const status = searchParams.get("status") || undefined
    const search = searchParams.get("search") || undefined

    // Get RBAC filter for user role
    const rbacFilter = getReportFilter(userRole)

    // If user has no access to any reports
    if (rbacFilter.id === "INVALID") {
      return NextResponse.json({
        success: true,
        data: {
          laporan: [],
          pagination: {
            page: 1,
            limit,
            total: 0,
            totalPages: 0
          },
          userPermissions: {
            canViewWBSDetail: false,
            canViewCOIDetail: false,
            canViewPMDetail: false,
          }
        }
      })
    }

    // Build where clause
    const where: any = {
      ...rbacFilter,
      ...(jenis && { jenis }),
      ...(status && { status }),
      ...(search && {
        OR: [
          { kronologi: { contains: search, mode: 'insensitive' } },
          { lokasi: { contains: search, mode: 'insensitive' } },
          { kategori: { contains: search, mode: 'insensitive' } },
          { pejabatDilaporkan: { contains: search, mode: 'insensitive' } },
          { jenisPengaduan: { contains: search, mode: 'insensitive' } }
        ]
      })
    }

    // Get total count
    const total = await prisma.laporan.count({ where })

    // Get laporan with pagination
    const laporan = await prisma.laporan.findMany({
      where,
      orderBy: { createdAt: "desc" },
      skip: (page - 1) * limit,
      take: limit,
      select: {
        id: true,
        jenis: true,
        status: true,
        kronologi: true,
        lokasi: true,
        kategori: true,
        buktiUrl: true,
        pejabatDilaporkan: true,
        jenisBenturan: true,
        jenisPengaduan: true,
        createdAt: true,
        updatedAt: true,
        // Include contact info for roles that can view details
        ...(checkAccess(userRole, "WBS", "viewDetail") ||
            checkAccess(userRole, "COI", "viewDetail") ||
            checkAccess(userRole, "PENGADUAN", "viewDetail") ? {
          kontakPelapor: {
            select: {
              email: true,
              noHp: true,
            }
          }
        } : {})
      }
    })

    const totalPages = Math.ceil(total / limit)



    return NextResponse.json({
      success: true,
      data: {
        laporan,
        pagination: {
          page,
          limit,
          total,
          totalPages
        },
        userPermissions: {
          canViewWBSDetail: checkAccess(userRole, "WBS", "viewDetail"),
          canViewCOIDetail: checkAccess(userRole, "COI", "viewDetail"),
          canViewPMDetail: checkAccess(userRole, "PENGADUAN", "viewDetail"),
        }
      }
    })

  } catch (error) {
    console.error("Error fetching laporan:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
