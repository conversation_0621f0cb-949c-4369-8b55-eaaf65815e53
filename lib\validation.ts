import * as z from "zod"
import { sanitizeInput, sanitizeLaporanText, sanitizeEmail, sanitizePhoneNumber, sanitizeGeneralText } from "./sanitize"
import { isValidUUID6 } from "./uuid-generator"

// Custom Zod transforms with sanitization
const sanitizedString = (maxLength: number = 500) => 
  z.string()
    .transform((val) => sanitizeInput(val))
    .refine((val) => val.length <= maxLength, {
      message: `Maksimal ${maxLength} karakter`
    })

const sanitizedGeneralText = (maxLength: number = 500) =>
  z.string()
    .transform((val) => sanitizeGeneralText(val))
    .refine((val) => val.length <= maxLength, {
      message: `Maksimal ${maxLength} karakter`
    })

const sanitizedLaporanText = () =>
  z.string()
    .transform((val) => sanitizeLaporanText(val))
    .refine((val) => val.length >= 50, {
      message: "Kronologi minimal 50 karakter"
    })
    .refine((val) => val.length <= 5000, {
      message: "Kronologi maksimal 5000 karakter"
    })

const sanitizedEmail = () =>
  z.string()
    .transform((val) => sanitizeEmail(val))
    .refine((val) => {
      if (val === '') return true // Optional field
      return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val)
    }, {
      message: "Format email tidak valid"
    })

const sanitizedPhoneNumber = () =>
  z.string()
    .transform((val) => sanitizePhoneNumber(val))
    .refine((val) => {
      if (val === '') return true // Optional field
      return /^[+]?[\d\s\-()]{8,20}$/.test(val)
    }, {
      message: "Format nomor telepon tidak valid"
    })

// UUID validation schema
const uuid6Schema = () =>
  z.string()
    .length(6, "UUID harus 6 karakter")
    .refine((val) => isValidUUID6(val), {
      message: "Format UUID tidak valid (harus 6 karakter alphanumeric)"
    })

// Enhanced form schemas with sanitization
export const wbsFormSchema = z.object({
  kategori: sanitizedGeneralText(100)
    .refine((val) => val.length > 0, {
      message: "Kategori harus dipilih"
    }),
  kronologi: sanitizedLaporanText(),
  buktiFile: z.any().optional(), // Make file upload optional like COI
  kontakEmail: sanitizedEmail().optional().or(z.literal("")),
  kontakHp: sanitizedPhoneNumber().optional().or(z.literal("")),
  hcaptchaToken: z.string().min(1, "Captcha harus diverifikasi"),
})

export const coiFormSchema = z.object({
  pejabatDilaporkan: sanitizedGeneralText(200)
    .refine((val) => val.length > 0, {
      message: "Nama pejabat harus diisi"
    }),
  jenisBenturan: sanitizedGeneralText(100)
    .refine((val) => val.length > 0, {
      message: "Jenis benturan kepentingan harus dipilih"
    }),
  kronologi: sanitizedLaporanText(),
  buktiFile: z.any().optional(),
  kontakEmail: sanitizedEmail().optional().or(z.literal("")),
  kontakHp: sanitizedPhoneNumber().optional().or(z.literal("")),
  hcaptchaToken: z.string().min(1, "Captcha harus diverifikasi"),
})

export const pengaduanFormSchema = z.object({
  jenisPengaduan: sanitizedGeneralText(100)
    .refine((val) => val.length > 0, {
      message: "Jenis pengaduan harus dipilih"
    }),
  lokasi: sanitizedGeneralText(300)
    .refine((val) => val.length > 0, {
      message: "Lokasi harus diisi"
    }),
  kronologi: sanitizedLaporanText(),
  buktiFile: z.any().optional(),
  kontakEmail: sanitizedEmail().optional().or(z.literal("")),
  kontakHp: sanitizedPhoneNumber().optional().or(z.literal("")),
  hcaptchaToken: z.string().min(1, "Captcha harus diverifikasi"),
})

// User management schema
export const userSchema = z.object({
  nama: sanitizedGeneralText(100)
    .refine((val) => val.length > 0, {
      message: "Nama harus diisi"
    }),
  email: sanitizedEmail()
    .refine((val) => val.length > 0, {
      message: "Email harus diisi"
    }),
  password: z.string()
    .min(6, "Password minimal 6 karakter")
    .max(100, "Password maksimal 100 karakter"),
  role: z.enum(["SUPER_ADMIN", "ADMIN", "ADMIN_WBS", "ADMIN_COI", "ADMIN_PM", "VERIFIKATOR", "INVESTIGATOR"]),
})

// API validation schemas
export const laporanApiSchema = z.object({
  jenis: z.enum(["WBS", "COI", "PENGADUAN"]),
  kronologi: sanitizedLaporanText(),
  hcaptchaToken: z.string().min(1, "Captcha token required"),

  // Optional fields based on jenis
  kategori: z.string().optional().or(z.literal("")).or(z.null()).transform(val => val === null || val === "" ? undefined : val),
  pejabatDilaporkan: z.string().optional().or(z.literal("")).or(z.null()).transform(val => val === null || val === "" ? undefined : val),
  jenisBenturan: z.string().optional().or(z.literal("")).or(z.null()).transform(val => val === null || val === "" ? undefined : val),
  jenisPengaduan: z.string().optional().or(z.literal("")).or(z.null()).transform(val => val === null || val === "" ? undefined : val),
  lokasi: z.string().optional().or(z.literal("")).or(z.null()).transform(val => val === null || val === "" ? undefined : val),

  // Contact info (optional)
  kontakEmail: z.string().optional().or(z.literal("")).or(z.null()).transform(val => val === null || val === "" ? undefined : val),
  kontakHp: z.string().optional().or(z.literal("")).or(z.null()).transform(val => val === null || val === "" ? undefined : val),
})

// Status update schema
export const statusUpdateSchema = z.object({
  status: z.enum(["BARU", "DIPROSES", "SELESAI"]),
})

// Export filter schema
export const exportFilterSchema = z.object({
  format: z.enum(["excel", "pdf"]).default("excel"),
  jenis: z.enum(["WBS", "COI", "PENGADUAN"]).optional(),
  status: z.enum(["BARU", "DIPROSES", "SELESAI"]).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

// File upload validation
export const fileUploadSchema = z.object({
  file: z.any()
    .refine((file) => {
      if (!file) return true // Optional
      return file.size <= 10 * 1024 * 1024 // 10MB
    }, "Ukuran file maksimal 10MB")
    .refine((file) => {
      if (!file) return true // Optional
      const allowedTypes = [
        "image/jpeg",
        "image/png", 
        "image/jpg",
        "application/pdf",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      ]
      return allowedTypes.includes(file.type)
    }, "Tipe file tidak didukung"),
})

// Type exports
export type WBSFormData = z.infer<typeof wbsFormSchema>
export type COIFormData = z.infer<typeof coiFormSchema>
export type PengaduanFormData = z.infer<typeof pengaduanFormSchema>
export type UserData = z.infer<typeof userSchema>
export type LaporanApiData = z.infer<typeof laporanApiSchema>
export type StatusUpdateData = z.infer<typeof statusUpdateSchema>
export type ExportFilterData = z.infer<typeof exportFilterSchema>

// Validation helper functions
export function validateLaporanData(data: any, jenis: "WBS" | "COI" | "PENGADUAN") {
  const baseValidation = laporanApiSchema.safeParse(data)
  
  if (!baseValidation.success) {
    return baseValidation
  }

  // Additional validation based on jenis
  const validatedData = baseValidation.data
  
  switch (jenis) {
    case "WBS":
      if (!validatedData.kategori) {
        return {
          success: false,
          error: { issues: [{ message: "Kategori harus dipilih untuk laporan WBS" }] }
        }
      }
      break
      
    case "COI":
      if (!validatedData.pejabatDilaporkan || !validatedData.jenisBenturan) {
        return {
          success: false,
          error: { issues: [{ message: "Pejabat yang dilaporkan dan jenis benturan harus diisi" }] }
        }
      }
      break
      
    case "PENGADUAN":
      if (!validatedData.jenisPengaduan || !validatedData.lokasi) {
        return {
          success: false,
          error: { issues: [{ message: "Jenis pengaduan dan lokasi harus diisi" }] }
        }
      }
      break
  }

  return baseValidation
}

// Security validation
export function validateSecurityHeaders(headers: Headers) {
  const origin = headers.get("origin")
  const referer = headers.get("referer")
  const userAgent = headers.get("user-agent")
  
  // Basic security checks
  if (!userAgent || userAgent.length < 10) {
    return { valid: false, error: "Invalid user agent" }
  }
  
  // Add more security validations as needed
  return { valid: true }
}
