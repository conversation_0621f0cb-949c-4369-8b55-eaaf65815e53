"use client"

import { useState, useRef } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import <PERSON><PERSON>tcha from "@hcaptcha/react-hcaptcha"
import { coiFormSchema, type COIFormData } from "@/lib/validation"
import { sanitizeFormInput } from "@/lib/sanitize-client"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Navbar } from "@/components/layout/navbar"
import { toast } from "sonner"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Upload } from "lucide-react"
import Link from "next/link"
import { MultipleFileUpload } from "@/components/ui/multiple-file-upload"

// Using imported schema from validation.ts

const jenisBenturanOptions = [
  "Kepentingan Finansial",
  "Kepentingan Keluarga",
  "Kepentingan Pribadi",
  "Rangkap Jabatan",
  "Penyalahgunaan Informasi",
  "Lainnya"
]

export default function COIPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [captchaToken, setCaptchaToken] = useState<string>("")
  const captchaRef = useRef<HCaptcha>(null)

  const form = useForm<COIFormData>({
    resolver: zodResolver(coiFormSchema),
    defaultValues: {
      pejabatDilaporkan: "",
      jenisBenturan: "",
      kronologi: "",
      buktiFile: null,
      kontakEmail: "",
      kontakHp: "",
      hcaptchaToken: "",
    },
  })

  const onSubmit = async (values: COIFormData) => {
    setIsSubmitting(true)

    try {
      // Check for invalid pejabatDilaporkan
      if (values.pejabatDilaporkan === ",m," || values.pejabatDilaporkan?.includes(",m,")) {
        toast.error("Nama pejabat tidak valid. Silakan masukkan nama yang benar.")
        setIsSubmitting(false)
        return
      }

      // Additional validation for empty or invalid names
      if (!values.pejabatDilaporkan || values.pejabatDilaporkan.trim().length < 3) {
        toast.error("Nama pejabat harus diisi minimal 3 karakter.")
        setIsSubmitting(false)
        return
      }

      // Validasi captcha token
      if (!values.hcaptchaToken) {
        toast.error("Captcha harus diverifikasi")
        setIsSubmitting(false)
        return
      }

      const formData = new FormData()

      // Required fields for COI
      formData.append("jenis", "COI")
      formData.append("pejabatDilaporkan", values.pejabatDilaporkan || "")
      formData.append("jenisBenturan", values.jenisBenturan || "")
      formData.append("kronologi", values.kronologi)
      formData.append("hcaptchaToken", values.hcaptchaToken)

      // Optional contact fields
      if (values.kontakEmail) {
        formData.append("kontakEmail", values.kontakEmail)
      }
      if (values.kontakHp) {
        formData.append("kontakHp", values.kontakHp)
      }

      // File attachments
      if (selectedFiles.length > 0) {
        selectedFiles.forEach((file) => {
          formData.append("buktiFile", file)
        })
      }

      // Validate required fields before sending
      const requiredFields = ['jenis', 'pejabatDilaporkan', 'jenisBenturan', 'kronologi', 'hcaptchaToken']
      const missingFields = requiredFields.filter(field => !formData.get(field))

      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(', ')}`)
      }

      const response = await fetch("/api/laporan", {
        method: "POST",
        body: formData,
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || "Gagal mengirim laporan")
      }

      // Check if result has the expected structure
      const laporanId = result.data?.id || result.id

      if (!laporanId) {
        throw new Error("ID laporan tidak ditemukan dalam response")
      }

      toast.success("Laporan COI berhasil dikirim!", {
        description: `ID Laporan: ${laporanId}. Simpan ID ini untuk referensi.`
      })

      // Reset form
      form.reset()
      setSelectedFiles([])
      setCaptchaToken("")
      captchaRef.current?.resetCaptcha()

      // Redirect ke halaman sukses dengan ID laporan
      router.push(`/success?id=${laporanId}&jenis=COI`)

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Gagal mengirim laporan"
      toast.error("Gagal mengirim laporan COI", {
        description: errorMessage
      })
    } finally {
      setIsSubmitting(false)
    }
  }



  const handleCaptchaVerify = (token: string) => {
    setCaptchaToken(token)
    form.setValue("hcaptchaToken", token)
    form.clearErrors("hcaptchaToken")
  }

  const handleCaptchaExpire = () => {
    setCaptchaToken("")
    form.setValue("hcaptchaToken", "")
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      <div className="max-w-4xl px-4 py-8 mx-auto sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Link href="/" className="inline-flex items-center mb-4 text-sm text-gray-500 hover:text-gray-700">
            <ArrowLeft className="w-4 h-4 mr-1" />
            Kembali ke Beranda
          </Link>
          
          <div className="flex items-center mb-4">
            <div className="flex items-center justify-center w-12 h-12 mr-4 bg-orange-100 rounded-lg">
              <AlertTriangle className="w-6 h-6 text-orange-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Benturan Kepentingan</h1>
              <p className="text-gray-600">Laporkan dugaan benturan kepentingan pejabat atau pegawai</p>
            </div>
          </div>
        </div>

        {/* Form */}
        <Card>
          <CardHeader>
            <CardTitle>Form Pelaporan Benturan Kepentingan</CardTitle>
            <CardDescription>
              Laporkan dugaan benturan kepentingan yang melibatkan pejabat atau pegawai. 
              Identitas pelapor bersifat opsional.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {/* Pejabat yang Dilaporkan */}
                <FormField
                  control={form.control}
                  name="pejabatDilaporkan"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Pejabat/Pegawai yang Dilaporkan *</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="Nama lengkap dan jabatan pejabat yang dilaporkan"
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        Sebutkan nama lengkap dan jabatan pejabat/pegawai yang diduga melakukan benturan kepentingan.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Jenis Benturan Kepentingan */}
                <FormField
                  control={form.control}
                  name="jenisBenturan"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Jenis Benturan Kepentingan *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih jenis benturan kepentingan" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {jenisBenturanOptions.map((jenis) => (
                            <SelectItem key={jenis} value={jenis}>
                              {jenis}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Kronologi */}
                <FormField
                  control={form.control}
                  name="kronologi"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Kronologi Kejadian *</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Jelaskan secara detail kronologi kejadian benturan kepentingan, bagaimana hal tersebut terjadi, siapa yang terlibat, kapan dan dimana..."
                          className="min-h-[150px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Minimal 50 karakter. Jelaskan bagaimana benturan kepentingan terjadi dan dampaknya.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Multiple File Upload */}
                <div className="space-y-2">
                  <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Bukti Pendukung (Opsional)
                  </label>
                  <MultipleFileUpload
                    selectedFiles={selectedFiles}
                    onFilesChange={setSelectedFiles}
                    maxFiles={5}
                    maxSize={10 * 1024 * 1024} // 10MB
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                  />
                  <p className="text-sm text-muted-foreground">
                    Upload dokumen, foto, atau file lain yang mendukung laporan benturan kepentingan Anda.
                  </p>
                </div>

                {/* Kontak Opsional */}
                <div className="p-4 border border-blue-200 rounded-lg bg-blue-50">
                  <h3 className="mb-2 text-sm font-medium text-blue-800">
                    Kontak Pelapor (Opsional)
                  </h3>
                  <p className="mb-4 text-xs text-blue-700">
                    Jika Anda bersedia dihubungi untuk klarifikasi lebih lanjut, silakan isi kontak di bawah. 
                    Informasi ini akan dijaga kerahasiaannya.
                  </p>
                  
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="kontakEmail"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email</FormLabel>
                          <FormControl>
                            <Input placeholder="<EMAIL>" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="kontakHp"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>No. HP</FormLabel>
                          <FormControl>
                            <Input placeholder="08xxxxxxxxxx" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* hCaptcha */}
                <FormField
                  control={form.control}
                  name="hcaptchaToken"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Verifikasi Captcha *</FormLabel>
                      <FormControl>
                        <div>
                          <div className={`flex justify-center p-4 rounded-lg border-2 ${
                            captchaToken ? 'border-green-200 bg-green-50' : 'border-orange-200 bg-orange-50'
                          }`}>
                            <HCaptcha
                              ref={captchaRef}
                              sitekey={process.env.NEXT_PUBLIC_HCAPTCHA_SITE_KEY || "10000000-ffff-ffff-ffff-000000000001"}
                              onVerify={handleCaptchaVerify}
                              onExpire={handleCaptchaExpire}
                              onError={() => {
                                toast.error("Terjadi kesalahan pada captcha")
                                setCaptchaToken("")
                                form.setValue("hcaptchaToken", "")
                              }}
                            />
                          </div>
                          {!captchaToken && (
                            <p className="mt-2 text-sm text-center text-orange-600">
                              ⚠️ Selesaikan captcha untuk mengaktifkan tombol kirim
                            </p>
                          )}
                          {captchaToken && (
                            <p className="mt-2 text-sm text-center text-green-600">
                              ✅ Captcha berhasil diverifikasi
                            </p>
                          )}
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Submit Button */}
                <div className="flex justify-end space-x-4">
                  <Link href="/">
                    <Button type="button" variant="outline">
                      Batal
                    </Button>
                  </Link>
                  <Button type="submit" disabled={isSubmitting || !captchaToken}>
                    {isSubmitting ? "Mengirim..." : (!captchaToken ? "Selesaikan Captcha untuk Melanjutkan" : "Kirim Laporan")}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* Info */}
        <div className="p-4 mt-8 border border-orange-200 rounded-lg bg-orange-50">
          <h3 className="mb-2 text-sm font-medium text-orange-800">
            Tentang Benturan Kepentingan
          </h3>
          <ul className="space-y-1 text-xs text-orange-700">
            <li>• Benturan kepentingan terjadi ketika kepentingan pribadi bertentangan dengan kepentingan publik</li>
            <li>• Dapat berupa kepentingan finansial, bisnis, politik, atau keluarga</li>
            <li>• Laporan akan diproses secara objektif dan profesional</li>
            <li>• Identitas pelapor akan dijaga kerahasiaannya</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
