const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createTestUsers() {
  try {
    console.log('🔄 Creating test users for RBAC testing...')

    const password = await bcrypt.hash('password123', 12)

    // Test users for each role
    const testUsers = [
      {
        email: '<EMAIL>',
        nama: 'Super Admin',
        role: 'SUPER_ADMIN',
        password
      },
      {
        email: '<EMAIL>',
        nama: 'Admin WBS',
        role: 'ADMIN_WBS',
        password
      },
      {
        email: '<EMAIL>',
        nama: 'Admin COI',
        role: 'ADMIN_COI',
        password
      },
      {
        email: '<EMAIL>',
        nama: 'Admin PM',
        role: 'ADMIN_PM',
        password
      }
    ]

    for (const user of testUsers) {
      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email: user.email }
      })

      if (existingUser) {
        console.log(`⚠️  User ${user.email} already exists, skipping...`)
        continue
      }

      // Create new user
      const newUser = await prisma.user.create({
        data: user
      })

      console.log(`✅ Created user: ${newUser.email} (${newUser.role})`)
    }

    console.log('\n🎉 Test users created successfully!')
    console.log('\n📋 Login Credentials:')
    console.log('Super Admin: <EMAIL> / password123')
    console.log('Admin WBS:   <EMAIL> / password123')
    console.log('Admin COI:   <EMAIL> / password123')
    console.log('Admin PM:    <EMAIL> / password123')

  } catch (error) {
    console.error('❌ Error creating test users:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestUsers()
