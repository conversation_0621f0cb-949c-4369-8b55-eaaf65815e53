"use client"

import Link from "next/link"
import { Button } from "@/components/ui/button"
import { useSession } from "next-auth/react"
import { LogIn, User } from "lucide-react"

export function MobileLoginButton() {
  const { data: session, status } = useSession()

  // Don't show anything while loading
  if (status === "loading") {
    return (
      <div className="h-9 w-16 bg-gray-200 animate-pulse rounded-md md:hidden" />
    )
  }

  // Don't show login button if user is already logged in
  if (session) {
    return (
      <div className="flex items-center md:hidden">
        <div className="flex items-center space-x-2 text-xs text-gray-600">
          <User className="h-4 w-4" />
          <span className="hidden xs:inline truncate max-w-20">
            {session.user?.name || 'Admin'}
          </span>
        </div>
      </div>
    )
  }

  // Show login button for non-authenticated users
  return (
    <Link href="/admin/login" className="md:hidden">
      <Button
        variant="outline"
        size="sm"
        className="h-9 px-3 text-xs font-medium border-gray-300 hover:border-primary hover:text-primary transition-colors"
      >
        <LogIn className="h-3 w-3 mr-1" />
        <span>Login</span>
      </Button>
    </Link>
  )
}

// Alternative floating login button for better visibility
export function MobileFloatingLoginButton() {
  const { data: session, status } = useSession()

  // Don't show if loading or already logged in
  if (status === "loading" || session) {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 z-40 md:hidden">
      <Link href="/admin/login">
        <Button
          size="icon"
          className="h-12 w-12 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 bg-primary hover:bg-primary/90"
        >
          <LogIn className="h-5 w-5" />
          <span className="sr-only">Login Admin</span>
        </Button>
      </Link>
    </div>
  )
}

// Sticky login banner for better visibility
export function MobileLoginBanner() {
  const { data: session, status } = useSession()

  // Don't show if loading or already logged in
  if (status === "loading" || session) {
    return null
  }

  return (
    <div className="sticky top-16 z-30 bg-blue-50 border-b border-blue-200 md:hidden">
      <div className="container px-4 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <LogIn className="h-4 w-4 text-blue-600" />
            <span className="text-sm text-blue-800 font-medium">
              Admin Access
            </span>
          </div>
          <Link href="/admin/login">
            <Button
              variant="outline"
              size="sm"
              className="h-8 px-3 text-xs bg-white border-blue-300 text-blue-700 hover:bg-blue-50"
            >
              Login
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
