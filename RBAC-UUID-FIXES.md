# 🔧 RBAC UUID Migration Fixes

Dokumentasi perbaikan RBAC system untuk kompatibilitas dengan UUID string format.

## ❌ **Original Error**

```
./app/api/dashboard/stats/route.ts:45:9
Type error: This comparison appears to be unintentional because the types 'string | undefined' and 'number' have no overlap.

  43 |
  44 |     // If user has no access to any reports, return empty stats
> 45 |     if (rbacFilter.id === -1) {
     |         ^
  46 |       return NextResponse.json({
  47 |         success: true,
  48 |         data: {
```

## ✅ **Root Cause**

Setelah migrasi ID dari integer ke UUID string, RBAC filter system masih menggunakan:
- ❌ `{ id: -1 }` untuk "no access" filter (number)
- ❌ Perbandingan dengan `-1` (number vs string)
- ❌ Regex pattern `\d+` untuk numeric ID matching

## 🔧 **Files Fixed**

### **1. lib/rbac.ts - RBAC Filter Functions**

#### **getReportFilter() Function:**
```typescript
// ❌ Before: Number-based "no access" filter
if (accessibleTypes.length === 0) {
  return { id: -1 } // This will return no results
}

// ✅ After: String-based "no access" filter
if (accessibleTypes.length === 0) {
  return { id: "INVALID" } // This will return no results (UUID that doesn't exist)
}
```

#### **getExportFilter() Function:**
```typescript
// ❌ Before: Number-based "no access" filter
if (exportableTypes.length === 0) {
  return { id: -1 } // This will return no results
}

// ✅ After: String-based "no access" filter
if (exportableTypes.length === 0) {
  return { id: "INVALID" } // This will return no results (UUID that doesn't exist)
}
```

### **2. app/api/admin/laporan/route.ts - Admin List Route**

```typescript
// ❌ Before: Number comparison
if (rbacFilter.id === -1) {

// ✅ After: String comparison
if (rbacFilter.id === "INVALID") {
```

### **3. app/api/dashboard/stats/route.ts - Dashboard Stats**

```typescript
// ❌ Before: Number comparison
if (rbacFilter.id === -1) {

// ✅ After: String comparison
if (rbacFilter.id === "INVALID") {
```

### **4. lib/rbac-middleware.ts - Middleware URL Matching**

```typescript
// ❌ Before: Numeric ID pattern
const reportIdMatch = pathname.match(/\/admin\/laporan\/(\d+)/)

// ✅ After: UUID pattern (6 alphanumeric characters)
const reportIdMatch = pathname.match(/\/admin\/laporan\/([A-Z0-9]{6})/)
```

## 🎯 **RBAC Filter Logic**

### **Filter Types:**

#### **1. Full Access Filter:**
```typescript
// User has access to all report types
return {} // Empty filter = no restrictions
```

#### **2. Partial Access Filter:**
```typescript
// User has access to specific report types
return {
  jenis: {
    in: ["WBS", "COI"] // Only these types
  }
}
```

#### **3. No Access Filter:**
```typescript
// User has no access to any reports
return { 
  id: "INVALID" // UUID that doesn't exist = no results
}
```

### **Usage in Queries:**

```typescript
// Prisma query with RBAC filter
const reports = await prisma.laporan.findMany({
  where: {
    ...rbacFilter, // Spread the filter
    // other conditions...
  }
})

// Results:
// - {} → All reports (no filter)
// - { jenis: { in: [...] } } → Filtered by type
// - { id: "INVALID" } → No reports (empty result)
```

## 🔍 **UUID Pattern Matching**

### **URL Structure:**
```
/admin/laporan/A1B2C3  ← UUID format
/admin/laporan/123     ← Old numeric format (no longer valid)
```

### **Regex Patterns:**

#### **Before (Numeric):**
```typescript
/\/admin\/laporan\/(\d+)/  // Matches: 123, 456, 789
```

#### **After (UUID):**
```typescript
/\/admin\/laporan\/([A-Z0-9]{6})/  // Matches: A1B2C3, X9Y8Z7, M4N5P6
```

## 🧪 **Testing RBAC Filters**

### **Test Cases:**

#### **1. Super Admin (Full Access):**
```typescript
const filter = getReportFilter("SUPER_ADMIN")
// Expected: {} (empty filter)
```

#### **2. WBS Admin (Partial Access):**
```typescript
const filter = getReportFilter("WBS_ADMIN")
// Expected: { jenis: { in: ["WBS"] } }
```

#### **3. Viewer (No Access):**
```typescript
const filter = getReportFilter("VIEWER")
// Expected: { id: "INVALID" }
```

### **Database Query Results:**

```sql
-- Full access (empty filter)
SELECT * FROM laporan;

-- Partial access (type filter)
SELECT * FROM laporan WHERE jenis IN ('WBS', 'COI');

-- No access (invalid ID filter)
SELECT * FROM laporan WHERE id = 'INVALID';  -- Returns 0 rows
```

## 🚀 **Build Status**

After these fixes:
- ✅ **TypeScript compilation**: No more type comparison errors
- ✅ **RBAC system**: Compatible with UUID format
- ✅ **Middleware**: Correctly matches UUID URLs
- ✅ **Database queries**: Use proper string filters
- ✅ **Access control**: Maintains security boundaries

## 📋 **Migration Checklist**

- [x] **RBAC filter functions** - Updated to use string "INVALID"
- [x] **Admin routes** - Updated filter comparisons
- [x] **Dashboard stats** - Updated filter comparisons
- [x] **Middleware** - Updated URL pattern matching
- [x] **Export functionality** - Compatible with new filters
- [x] **TypeScript types** - All comparisons now string-based

## 🔄 **Backward Compatibility**

### **Breaking Changes:**
- ❌ **Numeric IDs** no longer supported in URLs
- ❌ **Integer comparisons** replaced with string comparisons
- ❌ **Old URL patterns** `/admin/laporan/123` no longer match

### **Migration Path:**
1. **Update all ID references** to UUID format
2. **Run database migration** to convert existing IDs
3. **Update bookmarks/links** to use new UUID format
4. **Test all RBAC scenarios** with new filter system

## 🎯 **Security Implications**

### **Maintained Security:**
- ✅ **Access control** still enforced correctly
- ✅ **Role-based filtering** works as expected
- ✅ **No privilege escalation** possible
- ✅ **Invalid access** still blocked

### **Enhanced Security:**
- ✅ **Non-sequential IDs** harder to guess
- ✅ **UUID format** prevents ID enumeration attacks
- ✅ **Stronger validation** with UUID pattern matching

All RBAC system components are now fully compatible with UUID string format! 🎉
