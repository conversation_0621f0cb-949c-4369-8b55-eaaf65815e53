"use client"

import { useEffect } from 'react'

/**
 * Font Optimizer Component
 * Mengatasi masalah font preloading dan optimasi loading
 */
export function FontOptimizer() {
  useEffect(() => {
    // Preload critical fonts only when needed
    const preloadCriticalFonts = () => {
      // Check if font is actually being used
      const bodyElement = document.body
      const computedStyle = window.getComputedStyle(bodyElement)
      const fontFamily = computedStyle.fontFamily
      
      // Only preload if Geist font is being used
      if (fontFamily.includes('Geist')) {
        // Create preload link for critical font weights
        const preloadLink = document.createElement('link')
        preloadLink.rel = 'preload'
        preloadLink.as = 'font'
        preloadLink.type = 'font/woff2'
        preloadLink.crossOrigin = 'anonymous'
        
        // Add to head only if not already present
        const existingPreload = document.querySelector(`link[rel="preload"][as="font"]`)
        if (!existingPreload) {
          document.head.appendChild(preloadLink)
        }
      }
    }

    // Delay preload to avoid unused resource warning
    const timer = setTimeout(preloadCriticalFonts, 100)
    
    return () => clearTimeout(timer)
  }, [])

  return null // This component doesn't render anything
}

/**
 * Font Display Optimizer
 * Mengoptimalkan font-display untuk mengurangi layout shift
 */
export function FontDisplayOptimizer() {
  useEffect(() => {
    // Add font-display: swap to all font-face rules
    const addFontDisplaySwap = () => {
      const styleSheets = Array.from(document.styleSheets)
      
      styleSheets.forEach(sheet => {
        try {
          const rules = Array.from(sheet.cssRules || sheet.rules || [])
          
          rules.forEach(rule => {
            if (rule instanceof CSSFontFaceRule) {
              // Check if font-display is not already set
              // Use getPropertyValue/setProperty for better browser compatibility
              const currentFontDisplay = rule.style.getPropertyValue('font-display')
              if (!currentFontDisplay) {
                rule.style.setProperty('font-display', 'swap')
              }
            }
          })
        } catch (e) {
          // Ignore CORS errors for external stylesheets
          // console.debug('Cannot access stylesheet rules:', e)
        }
      })
    }

    // Run after fonts are loaded
    if (document.fonts && document.fonts.ready) {
      document.fonts.ready.then(addFontDisplaySwap)
    } else {
      // Fallback for browsers without FontFaceSet API
      setTimeout(addFontDisplaySwap, 1000)
    }
  }, [])

  return null
}

/**
 * Font Loading Performance Monitor
 * Monitor font loading performance dan memberikan feedback
 */
export function FontLoadingMonitor() {
  useEffect(() => {
    if (!document.fonts) return

    const monitorFontLoading = async () => {
      try {
        const startTime = performance.now()
        
        // Wait for all fonts to load
        await document.fonts.ready
        
        const endTime = performance.now()
        const loadTime = endTime - startTime
        
        // Log performance metrics (disabled to reduce console noise)
        // Uncomment below for font performance debugging
        /*
        if (process.env.NODE_ENV === 'development') {
          console.log(`🔤 Fonts loaded in ${loadTime.toFixed(2)}ms`)

          // Check for unused preloaded fonts
          const preloadedFonts = document.querySelectorAll('link[rel="preload"][as="font"]')
          const loadedFonts = Array.from(document.fonts)

          preloadedFonts.forEach(link => {
            const href = link.getAttribute('href')
            const isUsed = loadedFonts.some(font =>
              font.status === 'loaded' && href?.includes(font.family.toLowerCase())
            )

            if (!isUsed) {
              console.warn(`⚠️ Preloaded font not used: ${href}`)
            }
          })
        }
        */
      } catch (error) {
        // console.debug('Font loading monitoring failed:', error)
      }
    }

    monitorFontLoading()
  }, [])

  return null
}

/**
 * Combined Font Optimizer
 * Menggabungkan semua optimasi font dalam satu komponen
 */
export function CombinedFontOptimizer() {
  return (
    <>
      <FontOptimizer />
      <FontDisplayOptimizer />
      <FontLoadingMonitor />
    </>
  )
}
