# 🔤 Font Optimization Guide

Panduan untuk mengatasi peringatan font preloading dan mengoptimalkan loading font di aplikasi Next.js.

## ⚠️ **Ma<PERSON>ah yang Diatasi**

### **Peringatan Browser:**
```
The resource http://localhost:3000/_next/static/media/b0a57561b6cb5495.p.da1ebef7.woff2 
was preloaded using link preload but not used within a few seconds from the window's load event. 
Please make sure it has an appropriate `as` value and it is preloaded intentionally.
```

### **Penyebab Masalah:**
1. **Font di-preload** tetapi tidak langsung digunakan
2. **Konfigurasi font-display** yang tidak optimal
3. **Preloading font yang tidak diperlukan** (seperti mono font)
4. **Timing preload** yang tidak tepat

## ✅ **Solusi yang Diterapkan**

### **1. Optimasi Konfigurasi Font**

#### **Before (Bermasalah):**
```typescript
const geistSans = Geist({
  display: "block", // Menyebabkan flash
  preload: true,
  adjustFontFallback: false, // Tidak optimal
});

const geistMono = Geist_Mono({
  preload: false, // Tidak konsisten
  adjustFontFallback: false,
});
```

#### **After (Optimal):**
```typescript
const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap", // Optimal untuk performance
  preload: true, // Hanya untuk font utama
  fallback: ["system-ui", "arial"],
  adjustFontFallback: true, // Better fallback handling
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap", // Konsisten dengan main font
  preload: false, // Tidak preload font sekunder
  fallback: ["ui-monospace", "monospace"],
  adjustFontFallback: true,
});
```

### **2. Next.js Configuration Enhancement**

```javascript
// next.config.js
const nextConfig = {
  // Font optimization
  optimizeFonts: true,
  
  // Experimental features for better font loading
  experimental: {
    optimizePackageImports: ['@next/font'],
    fontLoaders: [
      {
        loader: '@next/font/google',
        options: {
          subsets: ['latin'],
          display: 'swap',
        },
      },
    ],
  },
};
```

### **3. Font Optimizer Component**

Dibuat komponen `CombinedFontOptimizer` yang menangani:

#### **FontOptimizer:**
- Preload font hanya ketika benar-benar digunakan
- Menghindari preload font yang tidak diperlukan
- Delayed preload untuk mengurangi peringatan

#### **FontDisplayOptimizer:**
- Menambahkan `font-display: swap` ke semua font-face rules
- Mengurangi layout shift
- Fallback untuk browser lama

#### **FontLoadingMonitor:**
- Monitor performa loading font
- Deteksi font yang di-preload tapi tidak digunakan
- Logging untuk development debugging

## 🎯 **Keuntungan Optimasi**

### **Performance Benefits:**
1. **Faster Initial Load** - Hanya preload font yang diperlukan
2. **Reduced Layout Shift** - Font-display: swap
3. **Better Fallback** - Improved fallback font handling
4. **Smaller Bundle** - Tidak preload font sekunder

### **User Experience:**
1. **No Font Flash** - Smooth font loading
2. **Faster Perceived Load** - System font fallback
3. **Better Mobile Performance** - Optimized for slow connections
4. **Consistent Typography** - Better fallback matching

### **Developer Experience:**
1. **No Console Warnings** - Clean development experience
2. **Performance Monitoring** - Font loading metrics
3. **Automatic Optimization** - Set and forget
4. **Debug Information** - Development logging

## 📊 **Font Loading Strategy**

### **Priority Levels:**

#### **High Priority (Preload):**
- ✅ **Geist Sans** - Main UI font
- ✅ **Critical weights** - Regular (400), Medium (500)

#### **Low Priority (No Preload):**
- ❌ **Geist Mono** - Code/monospace font
- ❌ **Extra weights** - Light, Bold, Black
- ❌ **Italic variants** - Unless specifically needed

### **Loading Sequence:**
1. **System fonts** load immediately (fallback)
2. **Critical fonts** preload and swap
3. **Secondary fonts** load on demand
4. **Font optimization** runs after load

## 🔧 **Implementation Details**

### **Font Display Values:**

```css
/* Optimal untuk most fonts */
font-display: swap;

/* Alternatives: */
font-display: block;    /* Flash prevention, slower */
font-display: fallback; /* Conservative approach */
font-display: optional; /* Performance first */
```

### **Preload Strategy:**

```html
<!-- Good: Critical font with proper attributes -->
<link rel="preload" as="font" type="font/woff2" 
      href="/fonts/geist-sans-regular.woff2" crossorigin>

<!-- Bad: Non-critical font preloaded -->
<link rel="preload" as="font" type="font/woff2" 
      href="/fonts/geist-mono-regular.woff2" crossorigin>
```

## 🧪 **Testing & Monitoring**

### **Performance Metrics:**
```javascript
// Font loading time
document.fonts.ready.then(() => {
  console.log('Fonts loaded in:', performance.now() - startTime, 'ms');
});

// Layout shift monitoring
new PerformanceObserver((list) => {
  for (const entry of list.getEntries()) {
    if (entry.hadRecentInput) continue;
    console.log('CLS:', entry.value);
  }
}).observe({entryTypes: ['layout-shift']});
```

### **Browser DevTools:**
1. **Network tab** - Check font loading timing
2. **Performance tab** - Monitor layout shifts
3. **Console** - Check for preload warnings
4. **Lighthouse** - Font optimization scores

## 🚀 **Best Practices**

### **Do's:**
- ✅ Use `font-display: swap` for most fonts
- ✅ Preload only critical fonts
- ✅ Provide good fallback fonts
- ✅ Monitor font loading performance
- ✅ Test on slow connections

### **Don'ts:**
- ❌ Preload all font weights/variants
- ❌ Use `font-display: block` unless necessary
- ❌ Ignore fallback font matching
- ❌ Load fonts synchronously
- ❌ Forget to test mobile performance

## 📱 **Mobile Considerations**

### **Optimizations for Mobile:**
1. **Reduced font variants** - Only essential weights
2. **Better fallbacks** - System fonts first
3. **Connection-aware loading** - Respect data saver
4. **Smaller font files** - Subset optimization

### **Network-Aware Loading:**
```javascript
// Respect user's data preferences
if (navigator.connection?.saveData) {
  // Skip non-critical font loading
  return;
}

// Load fonts based on connection speed
if (navigator.connection?.effectiveType === '4g') {
  // Load all font variants
} else {
  // Load only critical fonts
}
```

## 🔍 **Troubleshooting**

### **Common Issues:**

#### **"Font not used" Warning:**
- **Cause**: Font preloaded but not applied immediately
- **Solution**: Delay preload or check font usage

#### **Layout Shift:**
- **Cause**: Font swap causing text reflow
- **Solution**: Better fallback font matching

#### **Slow Font Loading:**
- **Cause**: Too many fonts preloaded
- **Solution**: Prioritize critical fonts only

### **Debug Commands:**
```javascript
// Check loaded fonts
console.log(Array.from(document.fonts));

// Check font loading status
document.fonts.forEach(font => {
  console.log(`${font.family}: ${font.status}`);
});

// Monitor font loading
document.fonts.addEventListener('loadingdone', (event) => {
  console.log('Font loaded:', event.fontface.family);
});
```

## 📈 **Results**

Setelah optimasi:
- ✅ **No preload warnings** in console
- ✅ **Faster perceived load time**
- ✅ **Better Lighthouse scores**
- ✅ **Improved mobile performance**
- ✅ **Consistent font rendering**

Font optimization sekarang berjalan otomatis dan tidak memerlukan maintenance tambahan! 🎉
