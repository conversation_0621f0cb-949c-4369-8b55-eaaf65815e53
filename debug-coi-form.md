# COI Form Debug Guide

## 🔍 Enhanced Console Logging

I've added comprehensive console logging to both the frontend and backend to help debug the COI form submission issue. Here's what to look for:

### Frontend Logging (<PERSON>rowser Console)

When you submit the COI form, you'll see detailed logs in the browser console:

#### 1. Form Submission Debug Group
```
🔍 [COI SUBMIT] Form Submission Debug
├── 📝 Raw form values from react-hook-form
├── ✅ Form validation state
├── 📁 Selected files
├── 🔐 Captcha state
├── 🔧 Preparing FormData
├── 📋 FormData contents
└── 🔍 Schema Comparison - Expected vs Actual
```

#### 2. What to Check in Frontend Logs:
- **Form Values**: Are all required fields populated?
- **Validation State**: Are there any validation errors?
- **Captcha**: Is the captcha token present?
- **Files**: Are files properly attached?
- **Schema Comparison**: Does the data match expected format?

### Backend Logging (Server Console)

The API endpoint will log detailed information:

#### 1. API Request Processing Group
```
🚀 [API /api/laporan] POST Request Processing
├── 📋 Request headers
├── 📦 Parsing FormData
├── 📋 Received FormData entries
├── 📊 Summary of received data
├── 🔧 Extracting form data
├── 🔍 Prisma Schema vs Input Data Comparison
├── 🧹 Sanitizing form data
├── 💾 Saving to database
└── 🎉 Sending success response
```

## 📊 Schema Comparison

### Frontend Schema (COI Form)
```typescript
{
  pejabatDilaporkan: string (required),
  jenisBenturan: string (required),
  kronologi: string (required),
  buktiFile: File[] (optional),
  kontakEmail: string (optional),
  kontakHp: string (optional),
  hcaptchaToken: string (required)
}
```

### Prisma Database Schema
```prisma
model Laporan {
  id                Int           @id @default(autoincrement())
  jenis             JenisLaporan  // "COI"
  kronologi         String        @db.Text
  buktiUrl          String?       // Single file (backward compatibility)
  buktiUrls         String?       // JSON array of multiple files
  status            StatusLaporan @default(BARU)
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
  
  // COI-specific fields
  pejabatDilaporkan String?       // For COI
  jenisBenturan     String?       // For COI
  
  // Relations
  kontakPelapor     KontakPelapor?
}

model KontakPelapor {
  id        Int     @id @default(autoincrement())
  email     String?
  noHp      String?
  laporanId Int     @unique
  laporan   Laporan @relation(fields: [laporanId], references: [id])
}
```

### API FormData Mapping
```
Frontend Field     → API FormData Key    → Database Field
─────────────────────────────────────────────────────────
pejabatDilaporkan  → pejabatDilaporkan   → pejabatDilaporkan
jenisBenturan      → jenisBenturan       → jenisBenturan
kronologi          → kronologi           → kronologi
buktiFile          → buktiFile           → buktiUrls (JSON)
kontakEmail        → kontakEmail         → kontakPelapor.email
kontakHp           → kontakHp            → kontakPelapor.noHp
hcaptchaToken      → hcaptchaToken       → (validation only)
(hardcoded)        → jenis               → jenis ("COI")
```

## 🐛 Common Issues to Look For

### 1. **Kronologi Field Issues**
- Check if kronologi contains error messages instead of user input
- Look for: "Error:", "Gagal mengirim", "at onSubmit"

### 2. **Captcha Issues**
- Verify captcha token is present and valid
- Check both `captchaToken` state and `values.hcaptchaToken`

### 3. **File Upload Issues**
- Check file size (max 10MB per file)
- Verify file types are allowed
- Look at file count and names

### 4. **Validation Errors**
- Check form validation state
- Look for required field violations
- Verify data sanitization doesn't remove content

## 🔧 Testing Steps

1. **Open Browser Console** (F12 → Console tab)
2. **Navigate to** `http://localhost:3000/coi`
3. **Fill out the form** with test data:
   - Pejabat: "Test Official Name"
   - Jenis Benturan: Select any option
   - Kronologi: "This is a test conflict of interest report"
   - Email: "<EMAIL>" (optional)
   - Phone: "081234567890" (optional)
4. **Complete the captcha**
5. **Submit the form**
6. **Check console logs** for detailed debugging information

## 📋 Expected Success Flow

1. ✅ Form validation passes
2. ✅ Captcha token present
3. ✅ FormData properly constructed
4. ✅ API receives correct data
5. ✅ Data sanitization successful
6. ✅ Database insertion successful
7. ✅ Success response returned
8. ✅ Form redirects to success page

## 🚨 Error Indicators

Look for these error patterns:

### Frontend Errors:
- ❌ Form validation errors
- ❌ Missing captcha token
- ❌ Network request failures
- ❌ Invalid response format

### Backend Errors:
- ❌ Invalid origin
- ❌ Missing required fields
- ❌ Captcha verification failed
- ❌ Database insertion failed
- ❌ File upload errors

## 📞 Next Steps

After testing with the enhanced logging:

1. **Share the console logs** from both browser and server
2. **Identify the specific failure point**
3. **Check if data transformation is causing issues**
4. **Verify database connection and schema**
5. **Test with minimal data first** (no files, no contact info)

The enhanced logging will help pinpoint exactly where the submission is failing!
