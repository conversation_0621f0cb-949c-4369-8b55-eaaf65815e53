// Manual RBAC test without importing modules
const UserRole = {
  SUPER_ADMIN: 'SUPER_ADMIN',
  ADMIN: 'ADMIN',
  ADMIN_WBS: 'ADMIN_WBS',
  ADMIN_COI: 'ADMIN_COI',
  ADMIN_PM: 'ADMIN_PM'
}

const RBAC_RULES = {
  [UserRole.SUPER_ADMIN]: {
    WBS: ["viewList", "viewDetail", "create", "update", "delete", "export"],
    COI: ["viewList", "viewDetail", "create", "update", "delete", "export"],
    PENGADUAN: ["viewList", "viewDetail", "create", "update", "delete", "export"],
  },
  [UserRole.ADMIN]: {
    WBS: ["viewList", "viewDetail", "create", "update", "delete", "export"],
    COI: ["viewList", "viewDetail", "create", "update", "delete", "export"],
    PENGADUAN: ["viewList", "viewDetail", "create", "update", "delete", "export"],
  },
  [UserRole.ADMIN_WBS]: {
    WBS: ["viewList"],
    COI: [],
    PENGADUAN: [],
  },
  [UserRole.ADMIN_COI]: {
    WBS: [],
    COI: ["viewList", "viewDetail", "export"],
    PENGADUAN: [],
  },
  [UserRole.ADMIN_PM]: {
    WBS: [],
    COI: [],
    PENGADUAN: ["viewList", "viewDetail", "export"],
  },
}

function checkAccess(userRole, reportType, action) {
  const rolePermissions = RBAC_RULES[userRole]
  if (!rolePermissions) return false

  const reportPermissions = rolePermissions[reportType]
  if (!reportPermissions) return false

  return reportPermissions.includes(action)
}

function testRBACPermissions() {
  console.log('🔐 Testing RBAC Permissions...')
  console.log('=' .repeat(50))

  const roles = ['SUPER_ADMIN', 'ADMIN_WBS', 'ADMIN_COI', 'ADMIN_PM']
  const reportTypes = ['WBS', 'COI', 'PENGADUAN']
  const actions = ['viewList', 'viewDetail', 'export']

  roles.forEach(role => {
    console.log(`\n👤 ${role}:`)
    console.log('-' .repeat(30))

    // Test each report type
    reportTypes.forEach(reportType => {
      console.log(`\n📊 ${reportType} Permissions:`)

      actions.forEach(action => {
        const hasAccess = checkAccess(role, reportType, action)
        const status = hasAccess ? '✅' : '❌'
        console.log(`  ${status} ${action}: ${hasAccess}`)
      })
    })

    console.log('\n' + '=' .repeat(50))
  })

  // Test specific scenarios
  console.log('\n🧪 Specific Test Scenarios:')
  console.log('=' .repeat(30))

  console.log('\n1. Admin COI accessing COI detail:')
  const coiDetailAccess = checkAccess('ADMIN_COI', 'COI', 'viewDetail')
  console.log(`   Result: ${coiDetailAccess ? '✅ ALLOWED' : '❌ DENIED'}`)

  console.log('\n2. Admin PM accessing PENGADUAN detail:')
  const pmDetailAccess = checkAccess('ADMIN_PM', 'PENGADUAN', 'viewDetail')
  console.log(`   Result: ${pmDetailAccess ? '✅ ALLOWED' : '❌ DENIED'}`)

  console.log('\n3. Admin WBS accessing WBS detail:')
  const wbsDetailAccess = checkAccess('ADMIN_WBS', 'WBS', 'viewDetail')
  console.log(`   Result: ${wbsDetailAccess ? '✅ ALLOWED' : '❌ DENIED'}`)

  console.log('\n4. Admin COI accessing WBS detail:')
  const coiWbsAccess = checkAccess('ADMIN_COI', 'WBS', 'viewDetail')
  console.log(`   Result: ${coiWbsAccess ? '✅ ALLOWED' : '❌ DENIED'}`)

  console.log('\n5. Admin PM accessing COI detail:')
  const pmCoiAccess = checkAccess('ADMIN_PM', 'COI', 'viewDetail')
  console.log(`   Result: ${pmCoiAccess ? '✅ ALLOWED' : '❌ DENIED'}`)

  // Summary
  console.log('\n📊 RBAC Summary:')
  console.log('=' .repeat(20))
  console.log('✅ Super Admin: Full access to all report types')
  console.log('❌ Admin WBS: List access only (NO detail access)')
  console.log('✅ Admin COI: Full access to COI reports (including detail)')
  console.log('✅ Admin PM: Full access to PENGADUAN reports (including detail)')

  console.log('\n🎯 Expected Behavior:')
  console.log('- Admin COI should see detail buttons for COI reports')
  console.log('- Admin PM should see detail buttons for PENGADUAN reports')
  console.log('- Admin WBS should see lock icons (no detail access)')
  console.log('- Super Admin should see detail buttons for all reports')

  console.log('\n🚀 Test completed!')
}

// Run the test
testRBACPermissions()
