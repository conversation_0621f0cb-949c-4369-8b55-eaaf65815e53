"use client"

import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Search, Filter, Eye, Download, Lock, AlertTriangle } from "lucide-react"
import Link from "next/link"
import { checkAccess, getAccessibleReportTypes, getRoleDisplayName, ReportType } from "@/lib/rbac"
import { UserRole } from "@prisma/client"

interface Laporan {
  id: string // Changed from number to string for UUID
  jenis: string
  status: string
  createdAt: string
  kronologi: string
  kategori?: string
  pejabatDilaporkan?: string
  jenisPengaduan?: string
  lokasi?: string
  kontakPelapor?: {
    email?: string
    noHp?: string
  }
}

interface LaporanResponse {
  laporan: Laporan[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  userPermissions: {
    canViewWBSDetail: boolean
    canViewCOIDetail: boolean
    canViewPMDetail: boolean
  }
}

export default function LaporanPage() {
  const { data: session } = useSession()
  const [laporan, setLaporan] = useState<Laporan[]>([])
  const [loading, setLoading] = useState(true)
  const [userPermissions, setUserPermissions] = useState({
    canViewWBSDetail: false,
    canViewCOIDetail: false,
    canViewPMDetail: false,
  })
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  })

  // Filters
  const [search, setSearch] = useState("")
  const [jenisFilter, setJenisFilter] = useState("")
  const [statusFilter, setStatusFilter] = useState("")
  const [accessibleReportTypes, setAccessibleReportTypes] = useState<ReportType[]>([])
  const [accessError, setAccessError] = useState<string | null>(null)

  // Initialize user permissions and accessible report types
  useEffect(() => {
    if (session?.user?.role) {
      const userRole = session.user.role as UserRole
      const accessible = getAccessibleReportTypes(userRole, "viewList")
      setAccessibleReportTypes(accessible)

      // Set user permissions
      setUserPermissions({
        canViewWBSDetail: checkAccess(userRole, "WBS", "viewDetail"),
        canViewCOIDetail: checkAccess(userRole, "COI", "viewDetail"),
        canViewPMDetail: checkAccess(userRole, "PENGADUAN", "viewDetail"),
      })

      // Check if user has access to any reports
      if (accessible.length === 0) {
        setAccessError("You don't have access to view any reports.")
      } else {
        setAccessError(null)
      }
    }
  }, [session])

  useEffect(() => {
    if (session?.user?.role && accessibleReportTypes.length > 0) {
      fetchLaporan()
    }
  }, [pagination.page, search, jenisFilter, statusFilter, session, accessibleReportTypes])

  const fetchLaporan = async () => {
    if (!session?.user?.role || accessibleReportTypes.length === 0) {
      return
    }

    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      })

      if (search) params.append("search", search)
      if (jenisFilter) params.append("jenis", jenisFilter)
      if (statusFilter) params.append("status", statusFilter)

      const response = await fetch(`/api/admin/laporan?${params}`)
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          const data: LaporanResponse = result.data
          setLaporan(data.laporan)
          setPagination(data.pagination)
          setUserPermissions(data.userPermissions)
        } else {
          setAccessError(result.message || "Failed to fetch reports")
        }
      } else if (response.status === 403) {
        setAccessError("Access denied. You don't have permission to view reports.")
      } else {
        setAccessError("Failed to fetch reports. Please try again.")
      }
    } catch (error) {
      console.error("Error fetching laporan:", error)
      setAccessError("An error occurred while fetching reports.")
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "BARU":
        return <Badge variant="destructive">Baru</Badge>
      case "DIPROSES":
        return <Badge variant="default">Diproses</Badge>
      case "SELESAI":
        return <Badge variant="secondary">Selesai</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getJenisBadge = (jenis: string) => {
    switch (jenis) {
      case "WBS":
        return <Badge className="bg-blue-100 text-blue-800">WBS</Badge>
      case "COI":
        return <Badge className="bg-orange-100 text-orange-800">COI</Badge>
      case "PENGADUAN":
        return <Badge className="bg-green-100 text-green-800">Pengaduan</Badge>
      default:
        return <Badge variant="outline">{jenis}</Badge>
    }
  }

  const handleSearch = (value: string) => {
    setSearch(value)
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const handleJenisFilter = (value: string) => {
    setJenisFilter(value === "all" ? "" : value)
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const handleStatusFilter = (value: string) => {
    setStatusFilter(value === "all" ? "" : value)
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }))
  }

  // Show access error if user has no permissions
  if (accessError) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Daftar Laporan</h1>
          <p className="text-gray-600">Kelola laporan berdasarkan role Anda</p>
        </div>
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{accessError}</AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Daftar Laporan</h1>
        <div className="flex items-center justify-between">
          <p className="text-gray-600">
            Kelola laporan berdasarkan role Anda: {session?.user?.role ? getRoleDisplayName(session.user.role as UserRole) : ''}
          </p>
          <Badge variant="outline" className="text-xs">
            Akses: {accessibleReportTypes.join(", ") || "Tidak ada"}
          </Badge>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filter & Pencarian
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Cari laporan..."
                value={search}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={jenisFilter || "all"} onValueChange={handleJenisFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Semua Jenis" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Jenis</SelectItem>
                {accessibleReportTypes.includes("WBS") && (
                  <SelectItem value="WBS">WBS</SelectItem>
                )}
                {accessibleReportTypes.includes("COI") && (
                  <SelectItem value="COI">COI</SelectItem>
                )}
                {accessibleReportTypes.includes("PENGADUAN") && (
                  <SelectItem value="PENGADUAN">Pengaduan</SelectItem>
                )}
              </SelectContent>
            </Select>

            <Select value={statusFilter || "all"} onValueChange={handleStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Semua Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Status</SelectItem>
                <SelectItem value="BARU">Baru</SelectItem>
                <SelectItem value="DIPROSES">Diproses</SelectItem>
                <SelectItem value="SELESAI">Selesai</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Export
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Laporan ({pagination.total})</CardTitle>
              <CardDescription>
                Menampilkan {laporan.length} dari {pagination.total} laporan
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              <div className="text-center py-8 text-gray-500">
                Loading laporan...
              </div>
              {[...Array(5)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-16 bg-gray-200 rounded"></div>
                </div>
              ))}
            </div>
          ) : accessError ? (
            <div className="text-center py-8">
              <div className="text-red-600 mb-2">{accessError}</div>
              <Button onClick={fetchLaporan} variant="outline">
                Coba Lagi
              </Button>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>Jenis</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Tanggal</TableHead>
                    <TableHead>Detail</TableHead>
                    <TableHead>Kontak</TableHead>
                    <TableHead>Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {laporan.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                        Tidak ada laporan ditemukan
                      </TableCell>
                    </TableRow>
                  ) : (
                    laporan.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium">#{item.id}</TableCell>
                        <TableCell>{getJenisBadge(item.jenis)}</TableCell>
                        <TableCell>{getStatusBadge(item.status)}</TableCell>
                        <TableCell>
                          {new Date(item.createdAt).toLocaleDateString('id-ID', {
                            day: 'numeric',
                            month: 'short',
                            year: 'numeric'
                          })}
                        </TableCell>
                        <TableCell className="max-w-xs">
                          <div className="space-y-1">
                            {item.kategori && (
                              <p className="text-xs text-gray-600">Kategori: {item.kategori}</p>
                            )}
                            {item.pejabatDilaporkan && (
                              <p className="text-xs text-gray-600">Pejabat: {item.pejabatDilaporkan}</p>
                            )}
                            {item.jenisPengaduan && (
                              <p className="text-xs text-gray-600">Jenis: {item.jenisPengaduan}</p>
                            )}
                            {item.lokasi && (
                              <p className="text-xs text-gray-600">Lokasi: {item.lokasi}</p>
                            )}
                            <p className="text-sm text-gray-900 line-clamp-2">
                              {item.kronologi.substring(0, 100)}...
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          {item.kontakPelapor ? (
                            <div className="text-xs space-y-1">
                              {item.kontakPelapor.email && (
                                <p className="text-gray-600">{item.kontakPelapor.email}</p>
                              )}
                              {item.kontakPelapor.noHp && (
                                <p className="text-gray-600">{item.kontakPelapor.noHp}</p>
                              )}
                            </div>
                          ) : (
                            <span className="text-xs text-gray-400">Anonim</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {(() => {
                            const canViewDetail =
                              (item.jenis === "WBS" && userPermissions.canViewWBSDetail) ||
                              (item.jenis === "COI" && userPermissions.canViewCOIDetail) ||
                              (item.jenis === "PENGADUAN" && userPermissions.canViewPMDetail)

                            if (canViewDetail) {
                              return (
                                <Link href={`/admin/laporan/${item.id}`}>
                                  <Button variant="ghost" size="sm">
                                    <Eye className="h-4 w-4" />
                                  </Button>
                                </Link>
                              )
                            } else {
                              return (
                                <Button variant="ghost" size="sm" disabled title="Tidak ada akses untuk melihat detail">
                                  <Lock className="h-4 w-4" />
                                </Button>
                              )
                            }
                          })()}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>

              {/* Pagination */}
              {pagination.totalPages > 1 && (
                <div className="flex items-center justify-between mt-6">
                  <p className="text-sm text-gray-600">
                    Halaman {pagination.page} dari {pagination.totalPages}
                  </p>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={pagination.page === 1}
                    >
                      Sebelumnya
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={pagination.page === pagination.totalPages}
                    >
                      Selanjutnya
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
