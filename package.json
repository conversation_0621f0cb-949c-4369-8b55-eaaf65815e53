{"name": "wbs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx scripts/seed.ts", "db:studio": "prisma studio", "migrate:uuid": "tsx scripts/migrate-to-uuid.ts", "setup:uuid": "bash scripts/setup-uuid-migration.sh", "mobile:test": "npm run build && npm run start", "mobile:audit": "lighthouse http://localhost:3000 --preset=mobile --output=html --output-path=./mobile-audit.html", "mobile:analyze": "npm run build && npx @next/bundle-analyzer", "mobile:debug": "npm run dev -- --inspect"}, "dependencies": {"@hcaptcha/react-hcaptcha": "^1.12.1", "@hookform/resolvers": "^5.2.1", "@prisma/client": "^6.14.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-visually-hidden": "^1.2.3", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dompurify": "^3.2.6", "exceljs": "^4.4.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.539.0", "next": "15.4.6", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "nodemailer": "^6.10.1", "prisma": "^6.14.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "sanitize-html": "^2.17.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.17"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/dompurify": "^3.0.5", "@types/jspdf": "^1.3.3", "@types/node": "^20", "@types/nodemailer": "^7.0.0", "@types/react": "^19", "@types/react-dom": "^19", "@types/sanitize-html": "^2.16.0", "tailwindcss": "^4", "tsx": "^4.20.4", "tw-animate-css": "^1.3.7", "typescript": "^5"}}