"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { useState } from "react"
import { useSession, signOut } from "next-auth/react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  FileText,
  Users,
  Download,
  BarChart3,
  Menu,
  X,
  LogOut,
  User
} from "lucide-react"
import { getRoleDisplayName, canExportAny } from "@/lib/rbac"
import { UserRole } from "@prisma/client"

const navigation = [
  {
    name: "Dashboard",
    href: "/admin/dashboard",
    icon: BarChart3,
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    href: "/admin/laporan",
    icon: FileText,
  },
  {
    name: "Manajemen User",
    href: "/admin/users",
    icon: Users,
  },
  {
    name: "Export Data",
    href: "/admin/export",
    icon: Download,
  },
]

// Sidebar Content Component
function SidebarContent({ onItemClick }: { onItemClick?: () => void }) {
  const pathname = usePathname()
  const { data: session } = useSession()

  const userInitials = session?.user?.name
    ? session.user.name.split(' ').map(n => n[0]).join('').toUpperCase()
    : 'A'

  // Filter navigation based on user role
  const getFilteredNavigation = () => {
    if (!session?.user?.role) return navigation

    const userRole = session.user.role as UserRole

    return navigation.filter(item => {
      // Export menu only for users who can export
      if (item.href === "/admin/export") {
        return canExportAny(userRole)
      }
      // All other menus are accessible
      return true
    })
  }

  const filteredNavigation = getFilteredNavigation()

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header */}
      <div className="flex items-center flex-shrink-0 px-4 py-5 border-b">
        <h2 className="text-lg font-semibold text-gray-900">
          Admin Panel
        </h2>
      </div>

      {/* User Info */}
      {session?.user && (
        <div className="px-4 py-4 border-b bg-gray-50">
          <div className="flex items-center space-x-3">
            <Avatar className="h-10 w-10">
              <AvatarFallback className="bg-primary text-primary-foreground text-sm font-medium">
                {userInitials}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {session.user.name}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {getRoleDisplayName(session.user.role as UserRole)}
              </p>
            </div>
          </div>
        </div>
      )}
      <nav className="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
        {filteredNavigation.map((item) => {
          const isActive = pathname === item.href
          return (
            <Link
              key={item.name}
              href={item.href}
              onClick={onItemClick}
              className={cn(
                "group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-colors",
                isActive
                  ? "bg-primary text-primary-foreground"
                  : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              )}
            >
              <item.icon
                className={cn(
                  "mr-3 flex-shrink-0 h-5 w-5",
                  isActive
                    ? "text-primary-foreground"
                    : "text-gray-500 group-hover:text-gray-700"
                )}
              />
              {item.name}
            </Link>
          )
        })}
      </nav>

      {/* Logout Button */}
      {session?.user && (
        <div className="p-4 border-t bg-gray-50">
          <Button
            onClick={() => {
              signOut()
              onItemClick?.()
            }}
            variant="ghost"
            className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            <LogOut className="mr-3 h-5 w-5" />
            Logout
          </Button>
        </div>
      )}
    </div>
  )
}

export function AdminSidebar() {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      {/* Desktop Sidebar */}
      <div className="hidden md:flex md:flex-col md:w-64 md:bg-white md:border-r">
        <SidebarContent />
      </div>

      {/* Mobile Hamburger Menu */}
      <div className="md:hidden">
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="fixed top-4 left-4 z-40 bg-white shadow-md hover:bg-gray-50"
              aria-label="Open admin menu"
            >
              <Menu className="h-5 w-5" />
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="p-0 w-64">
            <SidebarContent onItemClick={() => setIsOpen(false)} />
          </SheetContent>
        </Sheet>
      </div>
    </>
  )
}
