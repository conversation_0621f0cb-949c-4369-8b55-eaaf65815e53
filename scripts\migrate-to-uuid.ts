/**
 * Migration Script: Convert ID dari Integer ke UUID 6 karakter
 * 
 * Script ini akan:
 * 1. Backup data existing
 * 2. Create new tables dengan UUID schema
 * 3. Migrate data dengan generate UUID untuk setiap record
 * 4. Drop old tables
 * 5. Rename new tables
 */

import { PrismaClient } from '@prisma/client'
import { generateUUID6 } from '../lib/uuid-generator'

const prisma = new PrismaClient()

interface OldLaporan {
  id: number
  jenis: string
  kategori: string | null
  kronologi: string
  buktiUrl: string | null
  buktiUrls: string | null
  status: string
  createdAt: Date
  updatedAt: Date
  pejabatDilaporkan: string | null
  jenisBenturan: string | null
  jenisPengaduan: string | null
  lokasi: string | null
}

interface OldKontakPelapor {
  id: number
  email: string | null
  noHp: string | null
  laporanId: number
}

async function migrateToUUID() {
  console.log('🚀 Starting migration to UUID...')
  
  try {
    // 1. Backup existing data
    console.log('📦 Backing up existing data...')
    const existingLaporan = await prisma.$queryRaw<OldLaporan[]>`
      SELECT * FROM laporan ORDER BY id
    `
    const existingKontak = await prisma.$queryRaw<OldKontakPelapor[]>`
      SELECT * FROM kontak_pelapor ORDER BY id
    `
    
    console.log(`Found ${existingLaporan.length} laporan records`)
    console.log(`Found ${existingKontak.length} kontak_pelapor records`)
    
    // 2. Create mapping dari old ID ke new UUID
    console.log('🔄 Creating ID mappings...')
    const laporanIdMap = new Map<number, string>()
    const kontakIdMap = new Map<number, string>()
    
    // Generate UUIDs untuk laporan
    for (const laporan of existingLaporan) {
      let uuid: string
      do {
        uuid = generateUUID6()
      } while (laporanIdMap.has(laporan.id) || Array.from(laporanIdMap.values()).includes(uuid))
      
      laporanIdMap.set(laporan.id, uuid)
    }
    
    // Generate UUIDs untuk kontak
    for (const kontak of existingKontak) {
      let uuid: string
      do {
        uuid = generateUUID6()
      } while (kontakIdMap.has(kontak.id) || Array.from(kontakIdMap.values()).includes(uuid))
      
      kontakIdMap.set(kontak.id, uuid)
    }
    
    console.log('✅ ID mappings created')
    
    // 3. Create temporary tables dengan UUID schema
    console.log('🏗️ Creating temporary tables...')
    
    await prisma.$executeRaw`
      CREATE TABLE laporan_new (
        id VARCHAR(6) PRIMARY KEY,
        jenis ENUM('WBS', 'COI', 'PENGADUAN') NOT NULL,
        kategori VARCHAR(191) NULL,
        kronologi TEXT NOT NULL,
        buktiUrl VARCHAR(191) NULL,
        buktiUrls VARCHAR(191) NULL,
        status ENUM('BARU', 'DIPROSES', 'SELESAI') NOT NULL DEFAULT 'BARU',
        createdAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
        updatedAt DATETIME(3) NOT NULL,
        pejabatDilaporkan VARCHAR(191) NULL,
        jenisBenturan VARCHAR(191) NULL,
        jenisPengaduan VARCHAR(191) NULL,
        lokasi VARCHAR(191) NULL
      )
    `
    
    await prisma.$executeRaw`
      CREATE TABLE kontak_pelapor_new (
        id VARCHAR(6) PRIMARY KEY,
        email VARCHAR(191) NULL,
        noHp VARCHAR(191) NULL,
        laporanId VARCHAR(6) NOT NULL UNIQUE,
        FOREIGN KEY (laporanId) REFERENCES laporan_new(id) ON DELETE CASCADE
      )
    `
    
    console.log('✅ Temporary tables created')
    
    // 4. Migrate data ke temporary tables
    console.log('📊 Migrating data...')
    
    // Migrate laporan
    for (const laporan of existingLaporan) {
      const newId = laporanIdMap.get(laporan.id)!
      
      await prisma.$executeRaw`
        INSERT INTO laporan_new (
          id, jenis, kategori, kronologi, buktiUrl, buktiUrls, 
          status, createdAt, updatedAt, pejabatDilaporkan, 
          jenisBenturan, jenisPengaduan, lokasi
        ) VALUES (
          ${newId}, ${laporan.jenis}, ${laporan.kategori}, ${laporan.kronologi},
          ${laporan.buktiUrl}, ${laporan.buktiUrls}, ${laporan.status},
          ${laporan.createdAt}, ${laporan.updatedAt}, ${laporan.pejabatDilaporkan},
          ${laporan.jenisBenturan}, ${laporan.jenisPengaduan}, ${laporan.lokasi}
        )
      `
    }
    
    // Migrate kontak pelapor
    for (const kontak of existingKontak) {
      const newId = kontakIdMap.get(kontak.id)!
      const newLaporanId = laporanIdMap.get(kontak.laporanId)!
      
      await prisma.$executeRaw`
        INSERT INTO kontak_pelapor_new (id, email, noHp, laporanId)
        VALUES (${newId}, ${kontak.email}, ${kontak.noHp}, ${newLaporanId})
      `
    }
    
    console.log('✅ Data migrated to temporary tables')
    
    // 5. Drop old tables dan rename new tables
    console.log('🔄 Swapping tables...')
    
    await prisma.$executeRaw`DROP TABLE kontak_pelapor`
    await prisma.$executeRaw`DROP TABLE laporan`
    
    await prisma.$executeRaw`RENAME TABLE laporan_new TO laporan`
    await prisma.$executeRaw`RENAME TABLE kontak_pelapor_new TO kontak_pelapor`
    
    console.log('✅ Tables swapped successfully')
    
    // 6. Verify migration
    console.log('🔍 Verifying migration...')
    
    const newLaporanCount = await prisma.$queryRaw<[{count: number}]>`
      SELECT COUNT(*) as count FROM laporan
    `
    const newKontakCount = await prisma.$queryRaw<[{count: number}]>`
      SELECT COUNT(*) as count FROM kontak_pelapor
    `
    
    console.log(`✅ Migration completed successfully!`)
    console.log(`   - Laporan records: ${existingLaporan.length} → ${newLaporanCount[0].count}`)
    console.log(`   - Kontak records: ${existingKontak.length} → ${newKontakCount[0].count}`)
    
    // 7. Show sample of new UUIDs
    console.log('\n📋 Sample of new UUIDs:')
    const sampleLaporan = await prisma.$queryRaw<Array<{id: string, jenis: string}>>`
      SELECT id, jenis FROM laporan LIMIT 5
    `
    sampleLaporan.forEach(l => console.log(`   - ${l.jenis}: ${l.id}`))
    
  } catch (error) {
    console.error('❌ Migration failed:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run migration
if (require.main === module) {
  migrateToUUID()
    .then(() => {
      console.log('🎉 Migration completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Migration failed:', error)
      process.exit(1)
    })
}

export { migrateToUUID }
