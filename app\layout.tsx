import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Providers } from "./providers";
import { Toaster } from "@/components/ui/sonner";
import { MobilePerformanceOptimizer, MobileNetworkStatus, MobileViewportFix, MobilePerformanceMetrics } from "@/components/mobile/mobile-performance";
import { CombinedFontOptimizer } from "@/components/font-optimizer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap", // Use swap for better performance
  preload: true, // Only preload the main font
  fallback: ["system-ui", "arial"],
  adjustFontFallback: true, // Enable for better fallback handling
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap", // Use swap for mono font
  preload: false, // Don't preload mono font to reduce initial load
  fallback: ["ui-monospace", "monospace"],
  adjustFontFallback: true, // Enable for better fallback handling
});

export const metadata: Metadata = {
  title: "Whistleblowing System",
  description: "Sistem Pelaporan WBS, COI, dan Pengaduan Masyarakat",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "WBS System",
  },
  formatDetection: {
    telephone: false,
  },
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#ffffff" },
    { media: "(prefers-color-scheme: dark)", color: "#000000" },
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="id" className="scroll-smooth" data-scroll-behavior="smooth">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
        <meta name="theme-color" content="#ffffff" />

        {/* Modern mobile web app meta tags */}
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="WBS System" />

        {/* Additional PWA meta tags */}
        <meta name="application-name" content="WBS System" />
        <meta name="msapplication-TileColor" content="#ffffff" />
        <meta name="msapplication-config" content="/browserconfig.xml" />

        <meta name="format-detection" content="telephone=no" />

        {/* Font optimization - ensure immediate usage */}
        <style dangerouslySetInnerHTML={{
          __html: `
            /* Critical font loading */
            html, body, * {
              font-family: var(--font-geist-sans), system-ui, -apple-system, sans-serif;
            }
            /* Prevent font swap flash */
            .font-sans { font-family: var(--font-geist-sans), system-ui, sans-serif; }
            .font-mono { font-family: var(--font-geist-mono), ui-monospace, monospace; }
          `
        }} />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen bg-background font-sans touch-manipulation`}
      >
        <CombinedFontOptimizer />
        <MobilePerformanceOptimizer />
        <MobileNetworkStatus />
        <MobileViewportFix />
        <MobilePerformanceMetrics />

        <Providers>
          {children}
          <Toaster
            position="top-center"
            toastOptions={{
              duration: 4000,
              style: {
                background: 'var(--background)',
                color: 'var(--foreground)',
                border: '1px solid var(--border)',
              },
            }}
          />
        </Providers>
      </body>
    </html>
  );
}
