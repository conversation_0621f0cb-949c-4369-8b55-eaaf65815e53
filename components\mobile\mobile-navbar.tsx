"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTitle } from "@/components/ui/sheet"
import { useSession, signOut } from "next-auth/react"
import { Menu, X, Shield, Home, FileText, Settings, LogOut } from "lucide-react"
import { cn } from "@/lib/utils"

export function MobileNavbar() {
  const { data: session } = useSession()
  const [isOpen, setIsOpen] = useState(false)

  const navigationItems = [
    {
      href: "/",
      label: "Beranda",
      icon: Home,
      description: "Halaman utama"
    },
    {
      href: "/wbs",
      label: "Buat Laporan",
      icon: FileText,
      description: "Laporkan pelanggaran"
    },
    {
      href: "/coi",
      label: "Conflict of Interest",
      icon: Shield,
      description: "Laporan konflik kepentingan"
    },
    {
      href: "/pengaduan",
      label: "Pengaduan Masyarakat",
      icon: FileText,
      description: "Pengaduan umum"
    }
  ]

  const adminItems = session ? [
    {
      href: "/admin/dashboard",
      label: "Dashboard Admin",
      icon: Settings,
      description: "Panel administrasi"
    }
  ] : []

  const closeSheet = () => setIsOpen(false)

  return (
    <nav className="sticky top-0 z-50 w-full border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
      <div className="container flex h-16 items-center justify-between px-4">
        {/* Logo */}
        <Link 
          href="/" 
          className="flex items-center space-x-2 text-xl font-bold text-gray-900"
        >
          <Shield className="h-6 w-6 text-primary" />
          <span className="hidden sm:inline">WBS Sistem</span>
          <span className="sm:hidden">WBS</span>
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center space-x-6">
          {navigationItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className="text-sm font-medium text-gray-700 hover:text-primary transition-colors"
            >
              {item.label}
            </Link>
          ))}
          
          {session ? (
            <div className="flex items-center space-x-4">
              <Link
                href="/admin/dashboard"
                className="text-sm font-medium text-gray-700 hover:text-primary transition-colors"
              >
                Dashboard
              </Link>
              <Button
                variant="outline"
                size="sm"
                onClick={() => signOut()}
              >
                Logout
              </Button>
            </div>
          ) : (
            <Link href="/admin/login">
              <Button variant="outline" size="sm">
                Login Admin
              </Button>
            </Link>
          )}
        </div>

        {/* Mobile Menu */}
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetTrigger asChild className="md:hidden">
            <Button
              variant="ghost"
              size="icon"
              className="h-10 w-10"
              aria-label="Open menu"
            >
              <Menu className="h-5 w-5" />
            </Button>
          </SheetTrigger>
          
          <SheetContent side="right" className="w-80 sm:w-96">
            <SheetHeader className="text-left">
              <SheetTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-primary" />
                <span>WBS Sistem</span>
              </SheetTitle>
            </SheetHeader>

            <div className="mt-8 space-y-4">
              {/* Main Navigation */}
              <div className="space-y-2">
                <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wider">
                  Menu Utama
                </h3>
                {navigationItems.map((item) => {
                  const Icon = item.icon
                  return (
                    <Link
                      key={item.href}
                      href={item.href}
                      onClick={closeSheet}
                      className={cn(
                        "flex items-center space-x-3 rounded-lg px-3 py-3",
                        "text-gray-700 hover:bg-gray-100 hover:text-primary",
                        "transition-all duration-200",
                        "touch-manipulation active:scale-[0.98]"
                      )}
                    >
                      <Icon className="h-5 w-5" />
                      <div className="flex-1">
                        <div className="font-medium">{item.label}</div>
                        <div className="text-xs text-gray-500">{item.description}</div>
                      </div>
                    </Link>
                  )
                })}
              </div>

              {/* Admin Section */}
              <div className="border-t pt-4 space-y-2">
                <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wider">
                  Administrasi
                </h3>

                {session ? (
                  <>
                    {adminItems.map((item) => {
                      const Icon = item.icon
                      return (
                        <Link
                          key={item.href}
                          href={item.href}
                          onClick={closeSheet}
                          className={cn(
                            "flex items-center space-x-3 rounded-lg px-3 py-3",
                            "text-gray-700 hover:bg-gray-100 hover:text-primary",
                            "transition-all duration-200",
                            "touch-manipulation active:scale-[0.98]"
                          )}
                        >
                          <Icon className="h-5 w-5" />
                          <div className="flex-1">
                            <div className="font-medium">{item.label}</div>
                            <div className="text-xs text-gray-500">{item.description}</div>
                          </div>
                        </Link>
                      )
                    })}

                    <button
                      onClick={() => {
                        signOut()
                        closeSheet()
                      }}
                      className={cn(
                        "flex items-center space-x-3 rounded-lg px-3 py-3 w-full",
                        "text-red-600 hover:bg-red-50",
                        "transition-all duration-200",
                        "touch-manipulation active:scale-[0.98]"
                      )}
                    >
                      <LogOut className="h-5 w-5" />
                      <div className="flex-1 text-left">
                        <div className="font-medium">Logout</div>
                        <div className="text-xs text-red-500">Keluar dari sistem</div>
                      </div>
                    </button>
                  </>
                ) : (
                  <Link
                    href="/admin/login"
                    onClick={closeSheet}
                    className={cn(
                      "flex items-center space-x-3 rounded-lg px-3 py-3",
                      "text-gray-700 hover:bg-gray-100 hover:text-primary",
                      "transition-all duration-200",
                      "touch-manipulation active:scale-[0.98]"
                    )}
                  >
                    <Settings className="h-5 w-5" />
                    <div className="flex-1">
                      <div className="font-medium">Login Admin</div>
                      <div className="text-xs text-gray-500">Akses panel administrasi</div>
                    </div>
                  </Link>
                )}
              </div>

              {/* Footer Info */}
              <div className="border-t pt-4 text-center">
                <p className="text-xs text-gray-500">
                  Whistleblowing System
                </p>
                <p className="text-xs text-gray-400">
                  Sistem Pelaporan Terintegrasi
                </p>
              </div>
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </nav>
  )
}
