import { UserRole } from "@prisma/client"

// Report types
export type ReportType = "WBS" | "COI" | "PENGADUAN"

// Actions that can be performed
export type Action = "viewList" | "viewDetail" | "create" | "update" | "delete" | "export"

// RBAC Configuration
const RBAC_RULES = {
  [UserRole.SUPER_ADMIN]: {
    WBS: ["viewList", "viewDetail", "create", "update", "delete", "export"],
    COI: ["viewList", "viewDetail", "create", "update", "delete", "export"],
    PENGADUAN: ["viewList", "viewDetail", "create", "update", "delete", "export"],
  },
  [UserRole.ADMIN]: { // Legacy support - treat as SUPER_ADMIN
    WBS: ["viewList", "viewDetail", "create", "update", "delete", "export"],
    COI: ["viewList", "viewDetail", "create", "update", "delete", "export"],
    PENGADUAN: ["viewList", "viewDetail", "create", "update", "delete", "export"],
  },
  [UserRole.ADMIN_WBS]: {
    WBS: ["viewList"], // Can only view list, NOT detail, NO export
    COI: [],
    PENGADUAN: [],
  },
  [UserRole.ADMIN_COI]: {
    WBS: [],
    COI: ["viewList", "viewDetail", "export"], // Can view list, detail, and export COI
    PENGADUAN: [],
  },
  [UserRole.ADMIN_PM]: {
    WBS: [],
    COI: [],
    PENGADUAN: ["viewList", "viewDetail", "export"], // Can view list, detail, and export PM
  },
  [UserRole.VERIFIKATOR]: {
    WBS: ["viewList", "viewDetail"],
    COI: ["viewList", "viewDetail"],
    PENGADUAN: ["viewList", "viewDetail"],
  },
  [UserRole.INVESTIGATOR]: {
    WBS: ["viewList", "viewDetail"],
    COI: ["viewList", "viewDetail"],
    PENGADUAN: ["viewList", "viewDetail"],
  },
  [UserRole.USER]: {
    WBS: [],
    COI: [],
    PENGADUAN: [],
  },
} as const

/**
 * Check if user has access to perform specific action on report type
 * @param userRole - User's role
 * @param reportType - Type of report (WBS, COI, PENGADUAN)
 * @param action - Action to perform (viewList, viewDetail, etc.)
 * @returns boolean - true if user has access, false otherwise
 */
export function checkAccess(
  userRole: UserRole | string,
  reportType: ReportType,
  action: Action
): boolean {
  // Convert string to UserRole enum if needed
  const role = typeof userRole === 'string' ? userRole as UserRole : userRole
  
  // Check if role exists in RBAC rules
  if (!RBAC_RULES[role]) {
    console.warn(`Unknown role: ${role}`)
    return false
  }

  // Check if user has permission for this report type and action
  const permissions = RBAC_RULES[role][reportType] || []
  return permissions.includes(action)
}

/**
 * Get all report types that user can access for specific action
 * @param userRole - User's role
 * @param action - Action to check
 * @returns Array of report types user can access
 */
export function getAccessibleReportTypes(
  userRole: UserRole | string,
  action: Action
): ReportType[] {
  const role = typeof userRole === 'string' ? userRole as UserRole : userRole
  
  if (!RBAC_RULES[role]) {
    return []
  }

  const accessibleTypes: ReportType[] = []
  const reportTypes: ReportType[] = ["WBS", "COI", "PENGADUAN"]

  reportTypes.forEach(reportType => {
    if (checkAccess(role, reportType, action)) {
      accessibleTypes.push(reportType)
    }
  })

  return accessibleTypes
}

/**
 * Check if user is admin (any admin role)
 * @param userRole - User's role
 * @returns boolean - true if user is any type of admin
 */
export function isAdmin(userRole: UserRole | string): boolean {
  const role = typeof userRole === 'string' ? userRole as UserRole : userRole

  return [
    UserRole.SUPER_ADMIN,
    UserRole.ADMIN, // Legacy support
    UserRole.ADMIN_WBS,
    UserRole.ADMIN_COI,
    UserRole.ADMIN_PM,
  ].includes(role)
}

/**
 * Check if user is super admin
 * @param userRole - User's role
 * @returns boolean - true if user is super admin
 */
export function isSuperAdmin(userRole: UserRole | string): boolean {
  const role = typeof userRole === 'string' ? userRole as UserRole : userRole
  return role === UserRole.SUPER_ADMIN || role === UserRole.ADMIN // Legacy support
}

/**
 * Get user role display name
 * @param userRole - User's role
 * @returns string - Human readable role name
 */
export function getRoleDisplayName(userRole: UserRole | string): string {
  const role = typeof userRole === 'string' ? userRole as UserRole : userRole
  
  const roleNames = {
    [UserRole.SUPER_ADMIN]: "Super Admin",
    [UserRole.ADMIN]: "Admin (Legacy)", // Legacy support
    [UserRole.ADMIN_WBS]: "Admin WBS",
    [UserRole.ADMIN_COI]: "Admin COI",
    [UserRole.ADMIN_PM]: "Admin Pengaduan Masyarakat",
    [UserRole.VERIFIKATOR]: "Verifikator",
    [UserRole.INVESTIGATOR]: "Investigator",
    [UserRole.USER]: "User",
  }

  return roleNames[role] || role
}

/**
 * Get filter condition for database queries based on user role
 * @param userRole - User's role
 * @returns Object with where conditions for Prisma queries
 */
export function getReportFilter(userRole: UserRole | string) {
  const role = typeof userRole === 'string' ? userRole as UserRole : userRole

  // Super admin can see all reports
  if (role === UserRole.SUPER_ADMIN || role === UserRole.ADMIN) {
    return {}
  }

  // Get accessible report types for viewList action
  const accessibleTypes = getAccessibleReportTypes(role, "viewList")

  if (accessibleTypes.length === 0) {
    // No access to any reports
    return { id: "INVALID" } // This will return no results (UUID that doesn't exist)
  }

  // Return filter for accessible report types
  return {
    jenis: {
      in: accessibleTypes
    }
  }
}

/**
 * Get export filter condition for database queries based on user role
 * @param userRole - User's role
 * @returns Object with where conditions for export queries
 */
export function getExportFilter(userRole: UserRole | string) {
  const role = typeof userRole === 'string' ? userRole as UserRole : userRole

  // Super admin can export all reports
  if (role === UserRole.SUPER_ADMIN || role === UserRole.ADMIN) {
    return {}
  }

  // Get accessible report types for export action
  const exportableTypes = getAccessibleReportTypes(role, "export")

  if (exportableTypes.length === 0) {
    // No export access to any reports
    return { id: "INVALID" } // This will return no results (UUID that doesn't exist)
  }

  // Return filter for exportable report types
  return {
    jenis: {
      in: exportableTypes
    }
  }
}

/**
 * Check if user has any export permissions
 * @param userRole - User's role
 * @returns boolean - true if user can export any report type
 */
export function canExportAny(userRole: UserRole | string): boolean {
  const role = typeof userRole === 'string' ? userRole as UserRole : userRole

  return checkAccess(role, "WBS", "export") ||
         checkAccess(role, "COI", "export") ||
         checkAccess(role, "PENGADUAN", "export")
}

// Export types for use in other files
export type { ReportType, Action }
export { UserRole } from "@prisma/client"
