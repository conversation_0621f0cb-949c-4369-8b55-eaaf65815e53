"use client"

import React, { useState } from 'react'
import { formatUUIDForDisplay, isValidUUID6 } from '@/lib/uuid-generator'
import { Copy, Check } from 'lucide-react'
import { Button } from './button'
import { toast } from 'sonner'

interface UUIDDisplayProps {
  uuid: string
  label?: string
  showCopy?: boolean
  className?: string
}

/**
 * Component untuk menampilkan UUID dengan format yang rapi
 * Format: ABC-123 (dari ABC123)
 */
export function UUIDDisplay({ 
  uuid, 
  label = "ID", 
  showCopy = true, 
  className = "" 
}: UUIDDisplayProps) {
  const [copied, setCopied] = useState(false)
  
  if (!uuid || !isValidUUID6(uuid)) {
    return (
      <span className={`text-red-500 text-sm ${className}`}>
        Invalid UUID: {uuid}
      </span>
    )
  }
  
  const formattedUUID = formatUUIDForDisplay(uuid)
  
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(uuid)
      setCopied(true)
      toast.success(`${label} berhasil disalin: ${formattedUUID}`)
      
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      toast.error('Gagal menyalin ke clipboard')
    }
  }
  
  return (
    <div className={`inline-flex items-center gap-2 ${className}`}>
      <span className="font-mono text-sm font-medium bg-gray-100 px-2 py-1 rounded border">
        {formattedUUID}
      </span>
      
      {showCopy && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCopy}
          className="h-6 w-6 p-0"
          title={`Salin ${label}`}
        >
          {copied ? (
            <Check className="h-3 w-3 text-green-600" />
          ) : (
            <Copy className="h-3 w-3" />
          )}
        </Button>
      )}
    </div>
  )
}

interface UUIDInputProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  label?: string
  error?: string
  className?: string
}

/**
 * Component input untuk UUID dengan validasi real-time
 */
export function UUIDInput({
  value,
  onChange,
  placeholder = "Masukkan ID (6 karakter)",
  label,
  error,
  className = ""
}: UUIDInputProps) {
  const [inputValue, setInputValue] = useState(value)
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let newValue = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '')
    
    // Limit to 6 characters
    if (newValue.length > 6) {
      newValue = newValue.substring(0, 6)
    }
    
    setInputValue(newValue)
    onChange(newValue)
  }
  
  const isValid = !inputValue || isValidUUID6(inputValue)
  const showFormatted = inputValue.length === 6 && isValid
  
  return (
    <div className={className}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
        </label>
      )}
      
      <div className="relative">
        <input
          type="text"
          value={inputValue}
          onChange={handleChange}
          placeholder={placeholder}
          className={`
            block w-full px-3 py-2 border rounded-md shadow-sm font-mono
            ${isValid 
              ? 'border-gray-300 focus:border-blue-500 focus:ring-blue-500' 
              : 'border-red-300 focus:border-red-500 focus:ring-red-500'
            }
            ${showFormatted ? 'pr-20' : ''}
          `}
          maxLength={6}
        />
        
        {showFormatted && (
          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            <span className="text-xs text-gray-500 font-mono">
              {formatUUIDForDisplay(inputValue)}
            </span>
          </div>
        )}
      </div>
      
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
      
      {!isValid && inputValue && (
        <p className="mt-1 text-sm text-red-600">
          Format tidak valid. Gunakan 6 karakter (huruf A-Z dan angka 0-9)
        </p>
      )}
      
      {inputValue && inputValue.length < 6 && (
        <p className="mt-1 text-sm text-gray-500">
          {6 - inputValue.length} karakter lagi
        </p>
      )}
    </div>
  )
}

interface UUIDBadgeProps {
  uuid: string
  variant?: 'default' | 'success' | 'warning' | 'error'
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

/**
 * Component badge untuk menampilkan UUID dengan warna berbeda
 */
export function UUIDBadge({ 
  uuid, 
  variant = 'default', 
  size = 'md',
  className = "" 
}: UUIDBadgeProps) {
  if (!uuid || !isValidUUID6(uuid)) {
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800 ${className}`}>
        Invalid
      </span>
    )
  }
  
  const formattedUUID = formatUUIDForDisplay(uuid)
  
  const variantClasses = {
    default: 'bg-gray-100 text-gray-800',
    success: 'bg-green-100 text-green-800',
    warning: 'bg-yellow-100 text-yellow-800',
    error: 'bg-red-100 text-red-800'
  }
  
  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-1 text-sm',
    lg: 'px-3 py-1.5 text-base'
  }
  
  return (
    <span className={`
      inline-flex items-center rounded font-mono font-medium
      ${variantClasses[variant]}
      ${sizeClasses[size]}
      ${className}
    `}>
      {formattedUUID}
    </span>
  )
}
