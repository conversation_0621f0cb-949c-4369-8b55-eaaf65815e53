const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

// Extended data untuk testing RBAC dengan lebih banyak variasi
const extendedLaporanData = [
  // More WBS Reports (untuk testing Admin WBS access)
  {
    jenis: 'WBS',
    status: 'BARU',
    kronologi: 'Pelaporan dugaan mark-up anggaran renovasi gedung kantor sebesar 40%. Terdapat selisih harga yang signifikan antara RAB dengan harga pasar untuk material bangunan.',
    lokasi: 'Kantor Camat Tanah Abang, Jakarta Pusat',
    kategori: 'Mark-up Anggaran',
    pejabatDilaporkan: 'Camat Tanah Abang',
    buktiUrl: '/uploads/bukti-wbs-005.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567802'
      }
    }
  },
  {
    jenis: 'WBS',
    status: 'DIPROSES',
    kronologi: '<PERSON><PERSON><PERSON> mengenai penyalahgunaan dana operasional untuk keperluan pribadi pejabat. Dana yang dialokasikan untuk kegiatan dinas digunakan untuk liburan keluarga.',
    lokasi: 'Dinas Pariwisata Jakarta Barat',
    kategori: 'Penyalahgunaan Dana',
    pejabatDilaporkan: 'Kepala Dinas Pariwisata Jakbar',
    buktiUrl: '/uploads/bukti-wbs-006.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567803'
      }
    }
  },
  {
    jenis: 'WBS',
    status: 'SELESAI',
    kronologi: 'Whistleblowing terkait praktik nepotisme dalam rekrutmen pegawai kontrak. Proses seleksi tidak transparan dan menguntungkan kandidat tertentu yang memiliki hubungan keluarga.',
    lokasi: 'Badan Perencanaan Pembangunan Daerah Jakarta',
    kategori: 'Nepotisme',
    pejabatDilaporkan: 'Kepala Bappeda Jakarta',
    buktiUrl: '/uploads/bukti-wbs-007.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567804'
      }
    }
  },
  {
    jenis: 'WBS',
    status: 'SELESAI',
    kronologi: 'Laporan dugaan korupsi dalam pengadaan seragam sekolah yang tidak terbukti setelah dilakukan investigasi mendalam oleh tim pemeriksa internal.',
    lokasi: 'Dinas Pendidikan Jakarta Timur',
    kategori: 'Dugaan Korupsi',
    pejabatDilaporkan: 'Kepala Dinas Pendidikan Jaktim',
    buktiUrl: '/uploads/bukti-wbs-008.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567805'
      }
    }
  },

  // More COI Reports (untuk testing Admin COI access)
  {
    jenis: 'COI',
    status: 'BARU',
    kronologi: 'Konflik kepentingan dalam proses tender konsultan IT dimana pejabat yang memutuskan pemenang tender memiliki saham di perusahaan IT yang ikut tender.',
    lokasi: 'Dinas Komunikasi dan Informatika Jakarta',
    jenisBenturan: 'Kepentingan Finansial',
    pejabatDilaporkan: 'Kepala Dinas Kominfo Jakarta',
    buktiUrl: '/uploads/bukti-coi-005.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567806'
      }
    }
  },
  {
    jenis: 'COI',
    status: 'DIPROSES',
    kronologi: 'Benturan kepentingan dalam pemberian rekomendasi izin usaha dimana pejabat pemberi rekomendasi adalah mitra bisnis dari pemohon izin dalam usaha lain.',
    lokasi: 'Dinas Perindustrian Jakarta Utara',
    jenisBenturan: 'Kepentingan Bisnis',
    pejabatDilaporkan: 'Kepala Seksi Perizinan Disperindag',
    buktiUrl: '/uploads/bukti-coi-006.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567807'
      }
    }
  },
  {
    jenis: 'COI',
    status: 'SELESAI',
    kronologi: 'Konflik kepentingan dalam proses penilaian kinerja dimana atasan langsung memiliki hubungan keluarga dengan bawahan yang dinilai, sehingga penilaian menjadi tidak objektif.',
    lokasi: 'Sekretariat Daerah Jakarta Selatan',
    jenisBenturan: 'Kepentingan Keluarga',
    pejabatDilaporkan: 'Asisten Sekda Bidang Pemerintahan',
    buktiUrl: '/uploads/bukti-coi-007.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567808'
      }
    }
  },
  {
    jenis: 'COI',
    status: 'SELESAI',
    kronologi: 'Laporan dugaan konflik kepentingan yang tidak terbukti setelah dilakukan verifikasi. Tidak ditemukan hubungan kepentingan yang melanggar aturan.',
    lokasi: 'Dinas Lingkungan Hidup Jakarta',
    jenisBenturan: 'Kepentingan Finansial',
    pejabatDilaporkan: 'Kepala Bidang Pengendalian Pencemaran',
    buktiUrl: '/uploads/bukti-coi-008.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567809'
      }
    }
  },

  // More PENGADUAN Reports (untuk testing Admin PM access)
  {
    jenis: 'PENGADUAN',
    status: 'BARU',
    kronologi: 'Pengaduan masyarakat terkait pelayanan administrasi kependudukan yang lambat dan berbelit-belit. Proses pembuatan KTP memakan waktu hingga 2 bulan.',
    lokasi: 'Disdukcapil Jakarta Pusat',
    jenisPengaduan: 'Pelayanan Administrasi',
    buktiUrl: '/uploads/bukti-pengaduan-005.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567810'
      }
    }
  },
  {
    jenis: 'PENGADUAN',
    status: 'DIPROSES',
    kronologi: 'Keluhan mengenai fasilitas kesehatan yang tidak memadai di puskesmas. Alat medis rusak, obat sering kosong, dan ruang tunggu tidak nyaman.',
    lokasi: 'Puskesmas Kelapa Gading, Jakarta Utara',
    jenisPengaduan: 'Fasilitas Kesehatan',
    buktiUrl: '/uploads/bukti-pengaduan-006.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567811'
      }
    }
  },
  {
    jenis: 'PENGADUAN',
    status: 'SELESAI',
    kronologi: 'Pengaduan terkait ketidakadilan dalam pembagian bantuan sosial COVID-19. Beberapa keluarga yang berhak tidak mendapat bantuan sementara yang tidak berhak malah menerima.',
    lokasi: 'Kelurahan Kebon Jeruk, Jakarta Barat',
    jenisPengaduan: 'Bantuan Sosial',
    buktiUrl: '/uploads/bukti-pengaduan-007.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567812'
      }
    }
  },
  {
    jenis: 'PENGADUAN',
    status: 'SELESAI',
    kronologi: 'Pengaduan mengenai dugaan diskriminasi yang tidak terbukti setelah dilakukan investigasi. Tidak ditemukan bukti adanya perlakuan diskriminatif.',
    lokasi: 'Rumah Sakit Umum Jakarta Timur',
    jenisPengaduan: 'Dugaan Diskriminasi',
    buktiUrl: '/uploads/bukti-pengaduan-008.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567813'
      }
    }
  },

  // Additional mixed reports untuk comprehensive testing
  {
    jenis: 'WBS',
    status: 'BARU',
    kronologi: 'Laporan dugaan gratifikasi dalam proses perizinan lingkungan. Perusahaan memberikan "hadiah" kepada pejabat untuk mempermudah proses perizinan AMDAL.',
    lokasi: 'Dinas Lingkungan Hidup Jakarta Selatan',
    kategori: 'Gratifikasi',
    pejabatDilaporkan: 'Kepala Bidang AMDAL',
    buktiUrl: '/uploads/bukti-wbs-009.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567814'
      }
    }
  },
  {
    jenis: 'COI',
    status: 'BARU',
    kronologi: 'Konflik kepentingan dalam proses audit internal dimana auditor memiliki hubungan pribadi yang dekat dengan pihak yang diaudit sehingga hasil audit menjadi bias.',
    lokasi: 'Inspektorat Jakarta',
    jenisBenturan: 'Kepentingan Pribadi',
    pejabatDilaporkan: 'Auditor Madya Inspektorat',
    buktiUrl: '/uploads/bukti-coi-009.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567815'
      }
    }
  },
  {
    jenis: 'PENGADUAN',
    status: 'BARU',
    kronologi: 'Pengaduan masyarakat terkait kualitas air bersih yang buruk di wilayah pemukiman. Air keruh, berbau, dan tidak layak konsumsi namun tagihan tetap tinggi.',
    lokasi: 'Wilayah Cakung, Jakarta Timur',
    jenisPengaduan: 'Pelayanan Air Bersih',
    buktiUrl: '/uploads/bukti-pengaduan-009.pdf',
    kontakPelapor: {
      create: {
        email: '<EMAIL>',
        noHp: '081234567816'
      }
    }
  }
]

async function seedExtendedLaporan() {
  try {
    console.log('🌱 Starting extended laporan seeding...')
    console.log(`📝 Adding ${extendedLaporanData.length} additional records...`)
    
    for (let i = 0; i < extendedLaporanData.length; i++) {
      const data = extendedLaporanData[i]
      
      const laporan = await prisma.laporan.create({
        data: {
          ...data,
          createdAt: new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000), // Random date within last 60 days
          updatedAt: new Date()
        },
        include: {
          kontakPelapor: true
        }
      })

      console.log(`✅ Created ${laporan.jenis} report #${laporan.id} - ${laporan.status}`)
    }

    // Get comprehensive summary
    const summary = await prisma.laporan.groupBy({
      by: ['jenis', 'status'],
      _count: {
        id: true
      }
    })

    console.log('\n📊 Complete Database Summary:')
    console.log('============================')
    
    const totalByJenis = {}
    const totalByStatus = {}
    let grandTotal = 0

    summary.forEach(item => {
      const key = `${item.jenis} - ${item.status}`
      console.log(`${key}: ${item._count.id} records`)
      
      totalByJenis[item.jenis] = (totalByJenis[item.jenis] || 0) + item._count.id
      totalByStatus[item.status] = (totalByStatus[item.status] || 0) + item._count.id
      grandTotal += item._count.id
    })

    console.log('\n📈 Total by Jenis:')
    Object.entries(totalByJenis).forEach(([jenis, count]) => {
      console.log(`  ${jenis}: ${count} records`)
    })

    console.log('\n📊 Total by Status:')
    Object.entries(totalByStatus).forEach(([status, count]) => {
      console.log(`  ${status}: ${count} records`)
    })

    console.log(`\n🎉 Total laporan in database: ${grandTotal}`)
    console.log('✅ Extended laporan seeding completed successfully!')

    // RBAC Testing Summary
    console.log('\n🔐 RBAC Testing Data Summary:')
    console.log('============================')
    console.log(`Super Admin can access: ${grandTotal} records (all)`)
    console.log(`Admin WBS can access: ${totalByJenis.WBS || 0} records (WBS only, list view)`)
    console.log(`Admin COI can access: ${totalByJenis.COI || 0} records (COI only, full access)`)
    console.log(`Admin PM can access: ${totalByJenis.PENGADUAN || 0} records (PENGADUAN only, full access)`)

  } catch (error) {
    console.error('❌ Error seeding extended laporan:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run seeder
if (require.main === module) {
  seedExtendedLaporan()
    .catch((error) => {
      console.error('❌ Extended seeding failed:', error)
      process.exit(1)
    })
}

module.exports = { seedExtendedLaporan }
