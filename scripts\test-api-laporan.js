const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testAPILaporan() {
  try {
    console.log('🧪 Testing API Laporan functionality...')
    console.log('=' .repeat(50))

    // Test 1: Direct database query
    console.log('\n1. 📊 Direct Database Query:')
    const allLaporan = await prisma.laporan.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        jenis: true,
        status: true,
        kronologi: true,
        lokasi: true,
        createdAt: true
      }
    })

    console.log(`   Total records found: ${allLaporan.length}`)
    allLaporan.forEach(laporan => {
      console.log(`   - #${laporan.id}: ${laporan.jenis} - ${laporan.status}`)
      console.log(`     Location: ${laporan.lokasi}`)
      console.log(`     Created: ${laporan.createdAt.toLocaleDateString()}`)
    })

    // Test 2: RBAC Filter Testing
    console.log('\n2. 🔐 RBAC Filter Testing:')
    
    // Super Admin (no filter)
    const superAdminData = await prisma.laporan.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' }
    })
    console.log(`   Super Admin sees: ${superAdminData.length} records`)

    // Admin WBS (WBS only)
    const adminWBSData = await prisma.laporan.findMany({
      where: { jenis: 'WBS' },
      take: 5,
      orderBy: { createdAt: 'desc' }
    })
    console.log(`   Admin WBS sees: ${adminWBSData.length} WBS records`)

    // Admin COI (COI only)
    const adminCOIData = await prisma.laporan.findMany({
      where: { jenis: 'COI' },
      take: 5,
      orderBy: { createdAt: 'desc' }
    })
    console.log(`   Admin COI sees: ${adminCOIData.length} COI records`)

    // Admin PM (PENGADUAN only)
    const adminPMData = await prisma.laporan.findMany({
      where: { jenis: 'PENGADUAN' },
      take: 5,
      orderBy: { createdAt: 'desc' }
    })
    console.log(`   Admin PM sees: ${adminPMData.length} PENGADUAN records`)

    // Test 3: API Response Structure
    console.log('\n3. 📋 API Response Structure Test:')
    
    const mockAPIResponse = {
      success: true,
      data: {
        laporan: allLaporan,
        pagination: {
          page: 1,
          limit: 10,
          total: allLaporan.length,
          totalPages: Math.ceil(allLaporan.length / 10)
        },
        userPermissions: {
          canViewWBSDetail: true,
          canViewCOIDetail: true,
          canViewPMDetail: true,
        }
      }
    }

    console.log(`   Response structure: ✅ Valid`)
    console.log(`   Laporan array length: ${mockAPIResponse.data.laporan.length}`)
    console.log(`   Pagination: Page ${mockAPIResponse.data.pagination.page} of ${mockAPIResponse.data.pagination.totalPages}`)

    // Test 4: Sample Data Quality
    console.log('\n4. ✅ Data Quality Check:')
    
    if (allLaporan.length > 0) {
      const sample = allLaporan[0]
      console.log(`   Sample record #${sample.id}:`)
      console.log(`   - Jenis: ${sample.jenis}`)
      console.log(`   - Status: ${sample.status}`)
      console.log(`   - Has kronologi: ${sample.kronologi ? 'Yes' : 'No'}`)
      console.log(`   - Has lokasi: ${sample.lokasi ? 'Yes' : 'No'}`)
      console.log(`   - Created: ${sample.createdAt}`)
    }

    // Test 5: Count by Jenis
    console.log('\n5. 📊 Distribution by Jenis:')
    const distribution = await prisma.laporan.groupBy({
      by: ['jenis'],
      _count: { id: true }
    })

    distribution.forEach(item => {
      console.log(`   ${item.jenis}: ${item._count.id} records`)
    })

    // Test 6: Recent Records
    console.log('\n6. 🕒 Recent Records (Last 5):')
    const recentRecords = await prisma.laporan.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        jenis: true,
        status: true,
        lokasi: true,
        createdAt: true
      }
    })

    recentRecords.forEach((record, index) => {
      console.log(`   ${index + 1}. #${record.id} - ${record.jenis} (${record.status})`)
      console.log(`      ${record.lokasi}`)
      console.log(`      ${record.createdAt.toLocaleString()}`)
    })

    console.log('\n🎉 API Test Summary:')
    console.log('=' .repeat(25))
    console.log(`✅ Database connection: Working`)
    console.log(`✅ Data availability: ${allLaporan.length > 0 ? 'Available' : 'No data'}`)
    console.log(`✅ RBAC filtering: Ready`)
    console.log(`✅ API structure: Valid`)
    console.log(`✅ Recent data: ${recentRecords.length} records`)

    if (allLaporan.length === 0) {
      console.log('\n⚠️  WARNING: No laporan data found!')
      console.log('   Please run the seeder scripts:')
      console.log('   - node scripts/seed-laporan.js')
      console.log('   - node scripts/seed-laporan-extended.js')
    } else {
      console.log('\n🚀 API should be working correctly!')
    }

  } catch (error) {
    console.error('❌ Error testing API:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run test
if (require.main === module) {
  testAPILaporan()
    .catch((error) => {
      console.error('❌ API test failed:', error)
      process.exit(1)
    })
}

module.exports = { testAPILaporan }
