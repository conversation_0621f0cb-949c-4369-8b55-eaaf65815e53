"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"
import { Download, FileSpreadsheet, FileText, Calendar, AlertTriangle, Lock } from "lucide-react"
import { downloadPDF } from "@/lib/export"
import { canExportAny, checkAccess, getAccessibleReportTypes, getRoleDisplayName } from "@/lib/rbac"
import { UserRole } from "@prisma/client"

export default function ExportPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [hasExportAccess, setHasExportAccess] = useState(false)
  const [exportableTypes, setExportableTypes] = useState<string[]>([])
  const [filters, setFilters] = useState({
    jenis: "ALL",
    status: "ALL",
    startDate: "",
    endDate: "",
  })

  // Check export access on component mount
  useEffect(() => {
    if (session?.user?.role) {
      const userRole = session.user.role as UserRole
      const hasAccess = canExportAny(userRole)

      if (!hasAccess) {
        // Redirect if no export access
        router.push("/admin/dashboard")
        toast.error("Anda tidak memiliki akses untuk mengekspor data")
        return
      }

      setHasExportAccess(hasAccess)

      // Get exportable report types
      const exportable = getAccessibleReportTypes(userRole, "export")
      setExportableTypes(exportable)

      // Set default filter to first exportable type if not super admin
      if (exportable.length === 1) {
        setFilters(prev => ({ ...prev, jenis: exportable[0] }))
      }
    }
  }, [session, router])

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const exportData = async (format: "excel" | "pdf") => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      params.append("format", format)

      if (filters.jenis && filters.jenis !== "ALL") params.append("jenis", filters.jenis)
      if (filters.status && filters.status !== "ALL") params.append("status", filters.status)
      if (filters.startDate) params.append("startDate", filters.startDate)
      if (filters.endDate) params.append("endDate", filters.endDate)

      if (format === "excel") {
        // Download Excel file
        const response = await fetch(`/api/export?${params}`)
        if (!response.ok) {
          throw new Error("Gagal mengexport data")
        }

        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        
        // Get filename from response headers
        const contentDisposition = response.headers.get('content-disposition')
        const filename = contentDisposition?.split('filename=')[1]?.replace(/"/g, '') || 'laporan.xlsx'
        
        link.download = filename
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        
        toast.success("File Excel berhasil didownload")
      } else if (format === "pdf") {
        // Get data for PDF
        const response = await fetch(`/api/export?${params}`)
        if (!response.ok) {
          throw new Error("Gagal mengexport data")
        }

        const result = await response.json()
        
        // Generate filename
        const date = new Date().toISOString().split('T')[0]
        let filename = `laporan_${date}`
        if (filters.jenis) filename += `_${filters.jenis.toLowerCase()}`
        if (filters.status) filename += `_${filters.status.toLowerCase()}`
        filename += '.pdf'

        await downloadPDF(result.data, filename)
        toast.success("File PDF berhasil didownload")
      }
    } catch (error) {
      console.error("Export error:", error)
      toast.error("Gagal mengexport data")
    } finally {
      setLoading(false)
    }
  }

  const resetFilters = () => {
    setFilters({
      jenis: "ALL",
      status: "ALL",
      startDate: "",
      endDate: "",
    })
  }

  // Show loading or access denied
  if (!session) {
    return <div>Loading...</div>
  }

  if (!hasExportAccess) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Export Data</h1>
          <p className="text-gray-600">Export laporan ke format Excel atau PDF</p>
        </div>
        <Alert>
          <Lock className="h-4 w-4" />
          <AlertDescription>
            Anda tidak memiliki akses untuk mengekspor data. Role Anda: {getRoleDisplayName(session.user.role as UserRole)}
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Export Data</h1>
        <div className="flex items-center justify-between">
          <p className="text-gray-600">Export laporan ke format Excel atau PDF</p>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              Role: {getRoleDisplayName(session.user.role as UserRole)}
            </Badge>
            <Badge variant="secondary" className="text-xs">
              Akses: {exportableTypes.join(", ") || "Semua"}
            </Badge>
          </div>
        </div>
      </div>

      {/* Export Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Filter & Export
          </CardTitle>
          <CardDescription>
            Pilih filter dan format untuk mengexport data laporan
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label>Jenis Laporan</Label>
              <Select value={filters.jenis} onValueChange={(value) => handleFilterChange("jenis", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Semua Jenis" />
                </SelectTrigger>
                <SelectContent>
                  {exportableTypes.length > 1 && (
                    <SelectItem value="ALL">Semua Jenis</SelectItem>
                  )}
                  {exportableTypes.includes("WBS") && (
                    <SelectItem value="WBS">WBS</SelectItem>
                  )}
                  {exportableTypes.includes("COI") && (
                    <SelectItem value="COI">COI</SelectItem>
                  )}
                  {exportableTypes.includes("PENGADUAN") && (
                    <SelectItem value="PENGADUAN">Pengaduan</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Status</Label>
              <Select value={filters.status} onValueChange={(value) => handleFilterChange("status", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">Semua Status</SelectItem>
                  <SelectItem value="BARU">Baru</SelectItem>
                  <SelectItem value="DIPROSES">Diproses</SelectItem>
                  <SelectItem value="SELESAI">Selesai</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Tanggal Mulai</Label>
              <Input
                type="date"
                value={filters.startDate}
                onChange={(e) => handleFilterChange("startDate", e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label>Tanggal Akhir</Label>
              <Input
                type="date"
                value={filters.endDate}
                onChange={(e) => handleFilterChange("endDate", e.target.value)}
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between pt-4 border-t">
            <Button variant="outline" onClick={resetFilters}>
              Reset Filter
            </Button>
            
            <div className="flex gap-2">
              <Button
                onClick={() => exportData("excel")}
                disabled={loading}
                className="flex items-center gap-2"
              >
                <FileSpreadsheet className="h-4 w-4" />
                {loading ? "Mengexport..." : "Export Excel"}
              </Button>

              <Button
                onClick={() => exportData("pdf")}
                disabled={loading}
                variant="outline"
                className="flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                {loading ? "Mengexport..." : "Export PDF"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Info Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileSpreadsheet className="h-5 w-5 text-green-600" />
              Export Excel
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="text-sm text-gray-600 space-y-2">
              <li>• Format: .xlsx</li>
              <li>• Berisi semua kolom data</li>
              <li>• Dapat dibuka di Microsoft Excel</li>
              <li>• Mendukung filter dan sorting</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-red-600" />
              Export PDF
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="text-sm text-gray-600 space-y-2">
              <li>• Format: .pdf</li>
              <li>• Tampilan tabel yang rapi</li>
              <li>• Siap untuk dicetak</li>
              <li>• Kronologi dipotong untuk efisiensi</li>
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Usage Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Panduan Penggunaan
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-gray-600 space-y-2">
            <p><strong>Filter Tanggal:</strong> Gunakan filter tanggal untuk membatasi data berdasarkan periode tertentu.</p>
            <p><strong>Filter Jenis & Status:</strong> Pilih jenis laporan dan status untuk mendapatkan data yang lebih spesifik.</p>
            <p><strong>Export Excel:</strong> Cocok untuk analisis data lebih lanjut dan manipulasi data.</p>
            <p><strong>Export PDF:</strong> Cocok untuk laporan formal dan presentasi.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
