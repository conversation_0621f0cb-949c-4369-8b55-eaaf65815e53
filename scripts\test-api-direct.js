const fetch = require('node-fetch')

async function testAPIDirectly() {
  try {
    console.log('🧪 Testing API directly...')
    console.log('=' .repeat(40))

    // Test API endpoint directly
    const response = await fetch('http://localhost:3000/api/admin/laporan?page=1&limit=10', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Note: This won't work without proper session, but let's see the response
      }
    })

    console.log('📡 Response Status:', response.status)
    console.log('📡 Response Headers:', Object.fromEntries(response.headers.entries()))

    const data = await response.text()
    console.log('📊 Response Body:')
    console.log(data)

    if (response.status === 401) {
      console.log('\n✅ Expected 401 - API requires authentication')
    } else if (response.status === 200) {
      try {
        const jsonData = JSON.parse(data)
        console.log('\n📋 Parsed JSON:')
        console.log(JSON.stringify(jsonData, null, 2))
      } catch (e) {
        console.log('\n❌ Failed to parse JSON response')
      }
    }

  } catch (error) {
    console.error('❌ Error testing API:', error)
  }
}

// Run test
testAPIDirectly()
