"use client"

import { useEffect } from "react"

// Type definitions for Performance API
interface PerformanceEventTiming extends PerformanceEntry {
  processingStart?: number
  processingEnd?: number
  cancelable?: boolean
}

// Mobile analytics and tracking
export function MobileAnalytics() {
  useEffect(() => {
    // Track mobile device info
    const trackDeviceInfo = () => {
      const deviceInfo = {
        userAgent: navigator.userAgent,
        screenWidth: window.screen.width,
        screenHeight: window.screen.height,
        viewportWidth: window.innerWidth,
        viewportHeight: window.innerHeight,
        devicePixelRatio: window.devicePixelRatio,
        touchSupport: 'ontouchstart' in window,
        orientation: window.screen.orientation?.type || 'unknown',
        connectionType: (navigator as any).connection?.effectiveType || 'unknown',
        timestamp: new Date().toISOString(),
      }

      // Log to console for development only
      if (process.env.NODE_ENV === 'development') {
        console.log('Mobile Device Info:', deviceInfo)
      }

      // In production, send to analytics service
      // analytics.track('mobile_device_info', deviceInfo)
    }

    // Track form interactions
    const trackFormInteraction = (event: Event) => {
      const target = event.target as HTMLElement
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.tagName === 'SELECT') {
        if (process.env.NODE_ENV === 'development') {
          console.log('Form interaction:', {
            type: event.type,
            element: target.tagName,
            inputType: (target as HTMLInputElement).type,
            timestamp: new Date().toISOString(),
          })
        }
      }
    }

    // Track touch interactions
    const trackTouchInteraction = (event: TouchEvent) => {
      if (process.env.NODE_ENV === 'development') {
        console.log('Touch interaction:', {
          type: event.type,
          touches: event.touches.length,
          timestamp: new Date().toISOString(),
        })
      }
    }

    // Track scroll behavior
    let scrollTimeout: NodeJS.Timeout
    const trackScrollBehavior = () => {
      clearTimeout(scrollTimeout)
      scrollTimeout = setTimeout(() => {
        if (process.env.NODE_ENV === 'development') {
          console.log('Scroll position:', {
            scrollY: window.scrollY,
            scrollHeight: document.documentElement.scrollHeight,
            viewportHeight: window.innerHeight,
            scrollPercentage: Math.round((window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100),
            timestamp: new Date().toISOString(),
          })
        }
      }, 100)
    }

    // Initialize tracking
    trackDeviceInfo()

    // Add event listeners
    document.addEventListener('focus', trackFormInteraction, true)
    document.addEventListener('blur', trackFormInteraction, true)
    document.addEventListener('change', trackFormInteraction, true)
    document.addEventListener('touchstart', trackTouchInteraction, { passive: true })
    document.addEventListener('touchend', trackTouchInteraction, { passive: true })
    window.addEventListener('scroll', trackScrollBehavior, { passive: true })

    // Track orientation changes
    const handleOrientationChange = () => {
      setTimeout(() => {
        if (process.env.NODE_ENV === 'development') {
          console.log('Orientation changed:', {
            orientation: window.screen.orientation?.type || 'unknown',
            viewportWidth: window.innerWidth,
            viewportHeight: window.innerHeight,
            timestamp: new Date().toISOString(),
          })
        }
      }, 100)
    }

    window.addEventListener('orientationchange', handleOrientationChange)

    // Cleanup
    return () => {
      document.removeEventListener('focus', trackFormInteraction, true)
      document.removeEventListener('blur', trackFormInteraction, true)
      document.removeEventListener('change', trackFormInteraction, true)
      document.removeEventListener('touchstart', trackTouchInteraction)
      document.removeEventListener('touchend', trackTouchInteraction)
      window.removeEventListener('scroll', trackScrollBehavior)
      window.removeEventListener('orientationchange', handleOrientationChange)
      clearTimeout(scrollTimeout)
    }
  }, [])

  return null
}

// Mobile form analytics
export function MobileFormAnalytics({ formName }: { formName: string }) {
  useEffect(() => {
    const startTime = Date.now()
    let fieldInteractions: Record<string, number> = {}
    let errors: string[] = []

    const trackFieldInteraction = (event: Event) => {
      const target = event.target as HTMLInputElement
      const fieldName = target.name || target.id || 'unknown'
      
      if (event.type === 'focus') {
        fieldInteractions[fieldName] = (fieldInteractions[fieldName] || 0) + 1
      }
    }

    const trackFormError = (event: Event) => {
      const target = event.target as HTMLElement
      if (target.getAttribute('aria-invalid') === 'true') {
        const fieldName = (target as HTMLInputElement).name || target.id || 'unknown'
        if (!errors.includes(fieldName)) {
          errors.push(fieldName)
        }
      }
    }

    const trackFormSubmit = (event: Event) => {
      const completionTime = Date.now() - startTime
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`Form Analytics - ${formName}:`, {
          completionTime: completionTime / 1000, // seconds
          fieldInteractions,
          errors,
          timestamp: new Date().toISOString(),
        })
      }

      // In production, send to analytics
      // analytics.track('form_completion', {
      //   formName,
      //   completionTime,
      //   fieldInteractions,
      //   errors,
      // })
    }

    // Add event listeners
    document.addEventListener('focus', trackFieldInteraction, true)
    document.addEventListener('invalid', trackFormError, true)
    document.addEventListener('submit', trackFormSubmit, true)

    return () => {
      document.removeEventListener('focus', trackFieldInteraction, true)
      document.removeEventListener('invalid', trackFormError, true)
      document.removeEventListener('submit', trackFormSubmit, true)
    }
  }, [formName])

  return null
}

// Mobile performance monitoring
export function MobilePerformanceMonitor() {
  useEffect(() => {
    // Monitor Core Web Vitals
    const observeWebVitals = () => {
      // Largest Contentful Paint (LCP)
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        if (process.env.NODE_ENV === 'development') {
          console.log('LCP:', lastEntry.startTime)
        }
      })
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })

      // First Input Delay (FID)
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          // Type assertion for PerformanceEventTiming
          const eventEntry = entry as PerformanceEventTiming
          if (eventEntry.processingStart && process.env.NODE_ENV === 'development') {
            console.log('FID:', eventEntry.processingStart - eventEntry.startTime)
          }
        })
      })
      fidObserver.observe({ entryTypes: ['first-input'] })

      // Cumulative Layout Shift (CLS)
      let clsValue = 0
      const clsObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          // Type assertion for LayoutShift entry
          const layoutEntry = entry as any
          if (layoutEntry.hadRecentInput !== undefined && !layoutEntry.hadRecentInput) {
            clsValue += layoutEntry.value || 0
          }
        })
        if (process.env.NODE_ENV === 'development') {
          console.log('CLS:', clsValue)
        }
      })
      clsObserver.observe({ entryTypes: ['layout-shift'] })
    }

    // Monitor memory usage (if available)
    const monitorMemory = () => {
      if ('memory' in performance && process.env.NODE_ENV === 'development') {
        const memory = (performance as any).memory
        console.log('Memory Usage:', {
          used: Math.round(memory.usedJSHeapSize / 1048576), // MB
          total: Math.round(memory.totalJSHeapSize / 1048576), // MB
          limit: Math.round(memory.jsHeapSizeLimit / 1048576), // MB
        })
      }
    }

    // Monitor network information
    const monitorNetwork = () => {
      if ('connection' in navigator && process.env.NODE_ENV === 'development') {
        const connection = (navigator as any).connection
        console.log('Network Info:', {
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt,
          saveData: connection.saveData,
        })
      }
    }

    // Initialize monitoring
    if (typeof window !== 'undefined') {
      observeWebVitals()
      monitorMemory()
      monitorNetwork()

      // Monitor periodically
      const interval = setInterval(() => {
        monitorMemory()
        monitorNetwork()
      }, 30000) // Every 30 seconds

      return () => {
        clearInterval(interval)
      }
    }
  }, [])

  return null
}

// Mobile error tracking
export function MobileErrorTracking() {
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      if (process.env.NODE_ENV === 'development') {
        console.error('JavaScript Error:', {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          error: event.error?.stack,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString(),
        })
      }

      // In production, send to error tracking service
      // errorTracking.captureException(event.error)
    }

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      if (process.env.NODE_ENV === 'development') {
        console.error('Unhandled Promise Rejection:', {
          reason: event.reason,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString(),
        })
      }

      // In production, send to error tracking service
      // errorTracking.captureException(event.reason)
    }

    window.addEventListener('error', handleError)
    window.addEventListener('unhandledrejection', handleUnhandledRejection)

    return () => {
      window.removeEventListener('error', handleError)
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [])

  return null
}
