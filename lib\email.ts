import nodemailer from 'nodemailer'

interface EmailConfig {
  host: string
  port: number
  secure: boolean
  auth: {
    user: string
    pass: string
  }
}

interface SendEmailOptions {
  to: string
  subject: string
  html: string
  text?: string
}

// Create transporter (configure based on your email provider)
function createTransporter(): nodemailer.Transporter | null {
  try {
    // Example configuration for Gmail
    // You can modify this for other email providers
    const config: EmailConfig = {
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USER || '',
        pass: process.env.SMTP_PASS || '',
      },
    }

    // Skip email if not configured
    if (!config.auth.user || !config.auth.pass) {
      console.log('Email not configured, skipping email notification')
      return null
    }

    return nodemailer.createTransport(config)
  } catch (error) {
    console.error('Error creating email transporter:', error)
    return null
  }
}

export async function sendEmail(options: SendEmailOptions): Promise<boolean> {
  try {
    const transporter = createTransporter()
    
    if (!transporter) {
      return false
    }

    const mailOptions = {
      from: `"WBS System" <${process.env.SMTP_USER}>`,
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text,
    }

    await transporter.sendMail(mailOptions)
    console.log('Email sent successfully to:', options.to)
    return true
  } catch (error) {
    console.error('Error sending email:', error)
    return false
  }
}

// Email templates
export function generateLaporanConfirmationEmail(
  laporanId: string, // Changed from number to string for UUID
  jenis: string
): { subject: string; html: string; text: string } {
  const subject = `Konfirmasi Laporan ${jenis} #${laporanId}`
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .content { padding: 20px 0; }
        .footer { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 20px; font-size: 12px; color: #666; }
        .tracking-id { background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .tracking-id code { background: #fff; padding: 8px 12px; border-radius: 4px; font-size: 18px; font-weight: bold; color: #1976d2; }
        .button { display: inline-block; background: #1976d2; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h2>Laporan ${jenis} Berhasil Diterima</h2>
        </div>
        
        <div class="content">
          <p>Terima kasih telah mengirimkan laporan melalui sistem WBS kami.</p>
          
          <div class="tracking-id">
            <h3>ID Tracking Laporan Anda:</h3>
            <code>${laporanId}</code>
            <p><strong>Penting:</strong> Simpan ID ini untuk melacak status laporan Anda.</p>
          </div>
          
          <h3>Langkah Selanjutnya:</h3>
          <ol>
            <li>Simpan ID tracking laporan: <strong>${laporanId}</strong></li>
            <li>Gunakan ID tersebut untuk memantau status laporan</li>
            <li>Tim kami akan memproses laporan sesuai prosedur yang berlaku</li>
          </ol>
          
          <a href="${process.env.NEXTAUTH_URL}/tracking?id=${laporanId}" class="button">
            Tracking Laporan
          </a>
        </div>
        
        <div class="footer">
          <p>Email ini dikirim otomatis oleh sistem WBS. Jangan membalas email ini.</p>
          <p>Jika Anda memiliki pertanyaan, silakan hubungi tim support kami.</p>
        </div>
      </div>
    </body>
    </html>
  `
  
  const text = `
Laporan ${jenis} Berhasil Diterima

Terima kasih telah mengirimkan laporan melalui sistem WBS kami.

ID Tracking Laporan Anda: ${laporanId}

Penting: Simpan ID ini untuk melacak status laporan Anda.

Langkah Selanjutnya:
1. Simpan ID tracking laporan: ${laporanId}
2. Gunakan ID tersebut untuk memantau status laporan
3. Tim kami akan memproses laporan sesuai prosedur yang berlaku

Untuk tracking laporan, kunjungi: ${process.env.NEXTAUTH_URL}/tracking?id=${laporanId}

---
Email ini dikirim otomatis oleh sistem WBS. Jangan membalas email ini.
  `
  
  return { subject, html, text }
}

export function generateStatusUpdateEmail(
  laporanId: string, // Changed from number to string for UUID
  jenis: string,
  oldStatus: string,
  newStatus: string
): { subject: string; html: string; text: string } {
  const subject = `Update Status Laporan ${jenis} #${laporanId}`
  
  const getStatusText = (status: string) => {
    switch (status) {
      case 'BARU': return 'Baru'
      case 'DIPROSES': return 'Sedang Diproses'
      case 'SELESAI': return 'Selesai'
      default: return status
    }
  }
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .content { padding: 20px 0; }
        .footer { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 20px; font-size: 12px; color: #666; }
        .status-update { background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .button { display: inline-block; background: #1976d2; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h2>Update Status Laporan ${jenis} #${laporanId}</h2>
        </div>
        
        <div class="content">
          <p>Status laporan Anda telah diupdate.</p>
          
          <div class="status-update">
            <h3>Perubahan Status:</h3>
            <p><strong>Dari:</strong> ${getStatusText(oldStatus)}</p>
            <p><strong>Ke:</strong> ${getStatusText(newStatus)}</p>
          </div>
          
          <p>Untuk melihat detail lengkap dan timeline laporan, silakan klik tombol di bawah ini:</p>
          
          <a href="${process.env.NEXTAUTH_URL}/tracking?id=${laporanId}" class="button">
            Lihat Detail Laporan
          </a>
        </div>
        
        <div class="footer">
          <p>Email ini dikirim otomatis oleh sistem WBS. Jangan membalas email ini.</p>
          <p>Jika Anda memiliki pertanyaan, silakan hubungi tim support kami.</p>
        </div>
      </div>
    </body>
    </html>
  `
  
  const text = `
Update Status Laporan ${jenis} #${laporanId}

Status laporan Anda telah diupdate.

Perubahan Status:
Dari: ${getStatusText(oldStatus)}
Ke: ${getStatusText(newStatus)}

Untuk melihat detail lengkap dan timeline laporan, kunjungi: ${process.env.NEXTAUTH_URL}/tracking?id=${laporanId}

---
Email ini dikirim otomatis oleh sistem WBS. Jangan membalas email ini.
  `
  
  return { subject, html, text }
}

// Send notification when laporan is created
export async function sendLaporanConfirmation(
  email: string,
  laporanId: string, // Changed from number to string for UUID
  jenis: string
): Promise<boolean> {
  const emailContent = generateLaporanConfirmationEmail(laporanId, jenis)
  return await sendEmail({
    to: email,
    subject: emailContent.subject,
    html: emailContent.html,
    text: emailContent.text,
  })
}

// Send notification when status is updated
export async function sendStatusUpdateNotification(
  email: string,
  laporanId: string, // Changed from number to string for UUID
  jenis: string,
  oldStatus: string,
  newStatus: string
): Promise<boolean> {
  const emailContent = generateStatusUpdateEmail(laporanId, jenis, oldStatus, newStatus)
  return await sendEmail({
    to: email,
    subject: emailContent.subject,
    html: emailContent.html,
    text: emailContent.text,
  })
}
