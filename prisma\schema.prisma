// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// Enum untuk role user
enum UserRole {
  SUPER_ADMIN
  ADMIN_WBS
  ADMIN_COI
  ADMIN_PM
  ADMIN // Keep for migration compatibility
  VERIFIKATOR
  INVESTIGATOR
  USER
}

// Enum untuk jenis laporan
enum JenisLaporan {
  WBS
  COI
  PENGADUAN
}

// Enum untuk status laporan
enum StatusLaporan {
  BARU
  DIPROSES
  SELESAI
}

// Model User untuk admin, verifikator, investigator
model User {
  id        Int      @id @default(autoincrement())
  nama      String
  email     String   @unique
  password  String
  role      UserRole
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}

// Model utama untuk semua jenis laporan
model Laporan {
  id          String        @id @db.VarChar(6) // UUID 6 karakter (generated in code)
  jenis       JenisLaporan
  kategori    String?       // Opsional untuk WBS & COI
  kronologi   String        @db.Text
  buktiUrl    String?       // URL file bukti yang diupload (backward compatibility)
  buktiUrls   String?       // JSON string array of URLs for multiple files
  status      StatusLaporan @default(BARU)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relasi ke kontak pelapor (opsional)
  kontakPelapor KontakPelapor?

  // Field khusus untuk COI
  pejabatDilaporkan String? // Untuk COI
  jenisBenturan     String? // Untuk COI

  // Field khusus untuk Pengaduan
  jenisPengaduan String? // Untuk Pengaduan
  lokasi         String? // Untuk Pengaduan

  @@map("laporan")
}

// Model untuk kontak pelapor (opsional, terutama untuk WBS anonim)
model KontakPelapor {
  id        String  @id @db.VarChar(6) // UUID 6 karakter (generated in code)
  email     String?
  noHp      String?
  laporanId String  @unique @db.VarChar(6) // Foreign key ke Laporan

  // Relasi ke laporan
  laporan   Laporan @relation(fields: [laporanId], references: [id], onDelete: Cascade)

  @@map("kontak_pelapor")
}
