"use client"

import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { toast } from "sonner"
import { <PERSON>, Plus, Edit, Trash2, Shield } from "lucide-react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"

const userSchema = z.object({
  nama: z.string().min(1, "Nama harus diisi"),
  email: z.string().email("Format email tidak valid"),
  password: z.string().min(6, "Password minimal 6 karakter"),
  role: z.enum(["ADMIN", "VERIFIKATOR", "INVESTIGATOR", "SUPER_ADMIN", "ADMIN_WBS", "ADMIN_COI", "ADMIN_PM"]),
})

const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, "Password saat ini harus diisi"),
  newPassword: z.string().min(6, "Password baru minimal 6 karakter"),
  confirmPassword: z.string().min(1, "Konfirmasi password harus diisi"),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Konfirmasi password tidak cocok",
  path: ["confirmPassword"],
})

interface User {
  id: number
  nama: string
  email: string
  role: string
  createdAt: string
}

export default function UsersPage() {
  const { data: session } = useSession()
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [submitting, setSubmitting] = useState(false)
  const [showChangePasswordDialog, setShowChangePasswordDialog] = useState(false)
  const [changingPasswordUser, setChangingPasswordUser] = useState<User | null>(null)

  const form = useForm<z.infer<typeof userSchema>>({
    resolver: zodResolver(userSchema),
    defaultValues: {
      nama: "",
      email: "",
      password: "",
      role: "VERIFIKATOR",
    },
  })

  const changePasswordForm = useForm<z.infer<typeof changePasswordSchema>>({
    resolver: zodResolver(changePasswordSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  })

  useEffect(() => {
    fetchUsers()
  }, [])

  const fetchUsers = async () => {
    setLoading(true)
    try {
      const response = await fetch("/api/users")
      if (response.ok) {
        const data = await response.json()
        setUsers(data.data || [])
      }
    } catch (error) {
      console.error("Error fetching users:", error)
      toast.error("Gagal memuat data user")
    } finally {
      setLoading(false)
    }
  }

  const onSubmit = async (values: z.infer<typeof userSchema>) => {
    setSubmitting(true)
    try {
      const url = editingUser ? `/api/users/${editingUser.id}` : "/api/users"
      const method = editingUser ? "PATCH" : "POST"
      
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      })

      if (response.ok) {
        toast.success(editingUser ? "User berhasil diupdate" : "User berhasil dibuat")
        setShowCreateDialog(false)
        setEditingUser(null)
        form.reset()
        fetchUsers()
      } else {
        const error = await response.json()
        toast.error(error.error || "Gagal menyimpan user")
      }
    } catch (error) {
      console.error("Error saving user:", error)
      toast.error("Terjadi kesalahan saat menyimpan user")
    } finally {
      setSubmitting(false)
    }
  }

  const handleEdit = (user: User) => {
    setEditingUser(user)
    form.reset({
      nama: user.nama,
      email: user.email,
      password: "", // Don't pre-fill password
      role: user.role as any,
    })
    setShowCreateDialog(true)
  }

  const handleDelete = async (userId: number) => {
    if (!confirm("Apakah Anda yakin ingin menghapus user ini?")) {
      return
    }

    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: "DELETE",
      })

      if (response.ok) {
        toast.success("User berhasil dihapus")
        fetchUsers()
      } else {
        const error = await response.json()
        toast.error(error.error || "Gagal menghapus user")
      }
    } catch (error) {
      console.error("Error deleting user:", error)
      toast.error("Terjadi kesalahan saat menghapus user")
    }
  }

  const handleChangePassword = (user: User) => {
    setChangingPasswordUser(user)
    changePasswordForm.reset({
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    })
    setShowChangePasswordDialog(true)
  }

  const onChangePasswordSubmit = async (values: z.infer<typeof changePasswordSchema>) => {
    if (!changingPasswordUser) return

    setSubmitting(true)
    try {
      const response = await fetch(`/api/users/${changingPasswordUser.id}/change-password`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      })

      if (response.ok) {
        toast.success("Password berhasil diubah")
        setShowChangePasswordDialog(false)
        setChangingPasswordUser(null)
        changePasswordForm.reset()
      } else {
        const error = await response.json()
        toast.error(error.message || "Gagal mengubah password")
      }
    } catch (error) {
      console.error("Error changing password:", error)
      toast.error("Terjadi kesalahan saat mengubah password")
    } finally {
      setSubmitting(false)
    }
  }

  const getRoleBadge = (role: string) => {
    switch (role) {
      case "SUPER_ADMIN":
        return <Badge variant="destructive">Super Admin</Badge>
      case "ADMIN":
        return <Badge variant="destructive">Admin</Badge>
      case "ADMIN_WBS":
        return <Badge className="bg-blue-500 hover:bg-blue-600">Admin WBS</Badge>
      case "ADMIN_COI":
        return <Badge className="bg-orange-500 hover:bg-orange-600">Admin COI</Badge>
      case "ADMIN_PM":
        return <Badge className="bg-green-500 hover:bg-green-600">Admin PM</Badge>
      case "VERIFIKATOR":
        return <Badge variant="default">Verifikator</Badge>
      case "INVESTIGATOR":
        return <Badge variant="secondary">Investigator</Badge>
      default:
        return <Badge variant="outline">{role}</Badge>
    }
  }

  // Check if current user can change password for a specific user
  const canChangePassword = (user: User) => {
    if (!session?.user) return false

    // Super Admin and Admin can change any password
    if (session.user.role === "SUPER_ADMIN" || session.user.role === "ADMIN") {
      return true
    }

    // Admin WBS, Admin COI, Admin PM can only change their own password
    if (["ADMIN_WBS", "ADMIN_COI", "ADMIN_PM"].includes(session.user.role)) {
      return session.user.id === user.id.toString()
    }

    return false
  }

  // Check if current user can edit user details
  const canEditUser = (user: User) => {
    if (!session?.user) return false

    // Only Super Admin and Admin can edit user details
    return session.user.role === "SUPER_ADMIN" || session.user.role === "ADMIN"
  }

  // Check if current user can delete user
  const canDeleteUser = (user: User) => {
    if (!session?.user) return false

    // Only Super Admin and Admin can delete users
    // Cannot delete yourself
    return (session.user.role === "SUPER_ADMIN" || session.user.role === "ADMIN") &&
           session.user.id !== user.id.toString()
  }

  // Allow access to users page for all admin roles (for password change)
  const canAccessUsersPage = ["SUPER_ADMIN", "ADMIN", "ADMIN_WBS", "ADMIN_COI", "ADMIN_PM"].includes(session?.user?.role || "")

  // Only Super Admin and Admin can fully manage users
  const canFullyManageUsers = ["SUPER_ADMIN", "ADMIN"].includes(session?.user?.role || "")

  if (!canAccessUsersPage) {
    return (
      <div className="text-center py-12">
        <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h2 className="text-lg font-semibold text-gray-900 mb-2">Akses Terbatas</h2>
        <p className="text-gray-600">Anda tidak memiliki akses ke halaman ini.</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Manajemen User</h1>
          <p className="text-gray-600">Kelola user admin, verifikator, dan investigator</p>
        </div>
        
        {canFullyManageUsers && (
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Tambah User
              </Button>
            </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editingUser ? "Edit User" : "Tambah User Baru"}
              </DialogTitle>
              <DialogDescription>
                {editingUser ? "Update informasi user" : "Buat user baru untuk sistem"}
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="nama"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nama Lengkap</FormLabel>
                      <FormControl>
                        <Input placeholder="Masukkan nama lengkap" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Password {editingUser && "(kosongkan jika tidak ingin mengubah)"}
                      </FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="Masukkan password" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Role</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih role" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="SUPER_ADMIN">Super Admin</SelectItem>
                          <SelectItem value="ADMIN">Admin</SelectItem>
                          <SelectItem value="ADMIN_WBS">Admin WBS</SelectItem>
                          <SelectItem value="ADMIN_COI">Admin COI</SelectItem>
                          <SelectItem value="ADMIN_PM">Admin PM</SelectItem>
                          <SelectItem value="VERIFIKATOR">Verifikator</SelectItem>
                          <SelectItem value="INVESTIGATOR">Investigator</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setShowCreateDialog(false)
                      setEditingUser(null)
                      form.reset()
                    }}
                  >
                    Batal
                  </Button>
                  <Button type="submit" disabled={submitting}>
                    {submitting ? "Menyimpan..." : editingUser ? "Update" : "Simpan"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
        )}
      </div>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Daftar User ({users.length})
          </CardTitle>
          <CardDescription>
            Kelola akses user ke sistem admin
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-16 bg-gray-200 rounded"></div>
                </div>
              ))}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nama</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Tanggal Dibuat</TableHead>
                  <TableHead>Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                      Belum ada user
                    </TableCell>
                  </TableRow>
                ) : (
                  users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.nama}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>{getRoleBadge(user.role)}</TableCell>
                      <TableCell>
                        {new Date(user.createdAt).toLocaleDateString('id-ID')}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          {canEditUser(user) && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEdit(user)}
                              title="Edit User"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          )}

                          {canChangePassword(user) && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleChangePassword(user)}
                              className="text-blue-600 hover:text-blue-700"
                              title="Ubah Password"
                            >
                              <Shield className="h-4 w-4" />
                            </Button>
                          )}

                          {canDeleteUser(user) && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(user.id)}
                              className="text-red-600 hover:text-red-700"
                              title="Hapus User"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Change Password Dialog */}
      <Dialog open={showChangePasswordDialog} onOpenChange={setShowChangePasswordDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Ubah Password</DialogTitle>
            <DialogDescription>
              Ubah password untuk {changingPasswordUser?.nama}
            </DialogDescription>
          </DialogHeader>
          <Form {...changePasswordForm}>
            <form onSubmit={changePasswordForm.handleSubmit(onChangePasswordSubmit)} className="space-y-4">
              <FormField
                control={changePasswordForm.control}
                name="currentPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password Saat Ini</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="Masukkan password saat ini" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={changePasswordForm.control}
                name="newPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password Baru</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="Masukkan password baru" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={changePasswordForm.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Konfirmasi Password Baru</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="Konfirmasi password baru" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setShowChangePasswordDialog(false)
                    setChangingPasswordUser(null)
                    changePasswordForm.reset()
                  }}
                >
                  Batal
                </Button>
                <Button type="submit" disabled={submitting}>
                  {submitting ? "Mengubah..." : "Ubah Password"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  )
}
