"use client"

import { useEffect, useState, Suspense } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Navbar } from "@/components/layout/navbar"
import { CheckCircle, Copy, Search, Home, FileText } from "lucide-react"
import Link from "next/link"
import { toast } from "sonner"
import { UUIDDisplay, UUIDBadge } from "@/components/ui/uuid-display"
import { isValidUUID6 } from "@/lib/uuid-generator"

function SuccessPageContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [copied, setCopied] = useState(false)

  const laporanId = searchParams.get("id")
  const jenis = searchParams.get("jenis")

  useEffect(() => {
    if (!laporanId || !jenis) {
      router.push("/")
    }
  }, [laporanId, jenis, router])

  const copyToClipboard = async () => {
    if (laporanId) {
      try {
        await navigator.clipboard.writeText(laporanId)
        setCopied(true)
        toast.success("ID Laporan berhasil disalin!")
        setTimeout(() => setCopied(false), 2000)
      } catch (err) {
        toast.error("Gagal menyalin ID Laporan")
      }
    }
  }

  const getJenisBadge = (jenis: string) => {
    switch (jenis?.toUpperCase()) {
      case "WBS":
        return <Badge className="bg-blue-100 text-blue-800">WBS</Badge>
      case "COI":
        return <Badge className="bg-orange-100 text-orange-800">COI</Badge>
      case "PENGADUAN":
        return <Badge className="bg-green-100 text-green-800">Pengaduan</Badge>
      default:
        return <Badge variant="outline">{jenis}</Badge>
    }
  }

  const getJenisTitle = (jenis: string) => {
    switch (jenis?.toUpperCase()) {
      case "WBS":
        return "Whistleblowing System"
      case "COI":
        return "Conflict of Interest"
      case "PENGADUAN":
        return "Pengaduan Masyarakat"
      default:
        return "Laporan"
    }
  }

  if (!laporanId || !jenis) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Laporan Berhasil Dikirim!</h1>
          <p className="text-gray-600">
            Terima kasih telah melaporkan. Laporan Anda akan segera diproses oleh tim kami.
          </p>
        </div>

        {/* Laporan Info */}
        <Card className="mb-6">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  {getJenisTitle(jenis)}
                </CardTitle>
                <CardDescription>
                  Laporan Anda telah tersimpan dengan aman
                </CardDescription>
              </div>
              {getJenisBadge(jenis)}
            </div>
          </CardHeader>
          <CardContent>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-medium text-blue-800 mb-2">ID Laporan Anda</h3>
              <div className="flex items-center justify-center">
                {laporanId && isValidUUID6(laporanId) ? (
                  <UUIDDisplay
                    uuid={laporanId}
                    label="ID Laporan"
                    showCopy={true}
                    className="text-lg"
                  />
                ) : (
                  <div className="flex items-center gap-2">
                    <code className="bg-white px-3 py-2 rounded border text-lg font-mono text-blue-900 flex-1">
                      {laporanId}
                    </code>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={copyToClipboard}
                      className="flex items-center gap-1"
                    >
                      <Copy className="h-4 w-4" />
                      {copied ? "Tersalin!" : "Salin"}
                    </Button>
                  </div>
                )}
              </div>
              <p className="text-blue-700 text-sm mt-2">
                <strong>Penting:</strong> Simpan ID ini untuk melacak status laporan Anda
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Next Steps */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Langkah Selanjutnya</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-600 text-sm font-medium">1</span>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Simpan ID Laporan</h4>
                  <p className="text-gray-600 text-sm">
                    Catat atau simpan ID laporan <code className="bg-gray-100 px-1 rounded">{laporanId}</code> untuk tracking status
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-600 text-sm font-medium">2</span>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Pantau Status</h4>
                  <p className="text-gray-600 text-sm">
                    Gunakan fitur tracking untuk memantau progress penanganan laporan Anda
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-600 text-sm font-medium">3</span>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Tunggu Proses</h4>
                  <p className="text-gray-600 text-sm">
                    Tim kami akan memproses laporan sesuai prosedur yang berlaku
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          <Link href={`/tracking?id=${laporanId}`} className="flex-1">
            <Button className="w-full flex items-center gap-2">
              <Search className="h-4 w-4" />
              Tracking Laporan
            </Button>
          </Link>
          
          <Link href="/" className="flex-1">
            <Button variant="outline" className="w-full flex items-center gap-2">
              <Home className="h-4 w-4" />
              Kembali ke Beranda
            </Button>
          </Link>
        </div>

        {/* Additional Info */}
        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 className="font-medium text-yellow-800 mb-2">Informasi Penting</h4>
          <ul className="text-yellow-700 text-sm space-y-1">
            <li>• Laporan akan diproses sesuai dengan prosedur yang berlaku</li>
            <li>• Status laporan akan diupdate secara berkala</li>
            <li>• Jika Anda memberikan kontak, tim kami akan menghubungi jika diperlukan</li>
            <li>• Untuk laporan WBS, identitas Anda tetap terjaga kerahasiaannya</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default function SuccessPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <SuccessPageContent />
    </Suspense>
  )
}
