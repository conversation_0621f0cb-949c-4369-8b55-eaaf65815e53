import ExcelJS from 'exceljs'

interface LaporanExport {
  id: string // Changed from number to string for UUID
  jenis: string
  status: string
  createdAt: string
  kronologi: string
  kategori?: string | null
  pejabatDilaporkan?: string | null
  jenisBenturan?: string | null
  jenisPengaduan?: string | null
  lokasi?: string | null
  kontakEmail?: string | null
  kontakHp?: string | null
}

export async function exportToExcel(data: LaporanExport[], filename: string = 'laporan.xlsx') {
  // Create workbook
  const workbook = new ExcelJS.Workbook()
  workbook.creator = 'WBS System'
  workbook.lastModifiedBy = 'WBS System'
  workbook.created = new Date()
  workbook.modified = new Date()

  // Separate data by type
  const wbsData = data.filter(item => item.jenis === 'WBS')
  const coiData = data.filter(item => item.jenis === 'COI')
  const pengaduanData = data.filter(item => item.jenis === 'PENGADUAN')

  // Create All Reports sheet
  const allSheet = workbook.addWorksheet('Se<PERSON><PERSON>an')
  await createAllReportsSheet(allSheet, data)

  // Create WBS sheet if there's data
  if (wbsData.length > 0) {
    const wbsSheet = workbook.addWorksheet('Laporan WBS')
    await createWBSSheet(wbsSheet, wbsData)
  }

  // Create COI sheet if there's data
  if (coiData.length > 0) {
    const coiSheet = workbook.addWorksheet('Laporan COI')
    await createCOISheet(coiSheet, coiData)
  }

  // Create Pengaduan sheet if there's data
  if (pengaduanData.length > 0) {
    const pengaduanSheet = workbook.addWorksheet('Pengaduan Masyarakat')
    await createPengaduanSheet(pengaduanSheet, pengaduanData)
  }

  // Generate buffer
  const buffer = await workbook.xlsx.writeBuffer()
  return buffer
}

async function createAllReportsSheet(worksheet: ExcelJS.Worksheet, data: LaporanExport[]) {
  // Set headers
  const headers = [
    'ID', 'Jenis', 'Status', 'Tanggal Dibuat', 'Kategori/Detail',
    'Email Kontak', 'No HP Kontak', 'Kronologi'
  ]

  worksheet.addRow(headers)

  // Style header row
  const headerRow = worksheet.getRow(1)
  headerRow.font = { bold: true, color: { argb: 'FFFFFF' } }
  headerRow.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: '366092' }
  }
  headerRow.alignment = { horizontal: 'center', vertical: 'middle' }

  // Add data rows
  data.forEach(item => {
    const detail = item.kategori || item.pejabatDilaporkan || item.jenisPengaduan || '-'
    worksheet.addRow([
      item.id,
      item.jenis,
      item.status,
      new Date(item.createdAt).toLocaleDateString('id-ID'),
      detail,
      item.kontakEmail || '-',
      item.kontakHp || '-',
      item.kronologi.substring(0, 500) + (item.kronologi.length > 500 ? '...' : '')
    ])
  })

  // Set column widths
  worksheet.columns = [
    { width: 8 },   // ID
    { width: 12 },  // Jenis
    { width: 12 },  // Status
    { width: 15 },  // Tanggal
    { width: 25 },  // Detail
    { width: 25 },  // Email
    { width: 15 },  // HP
    { width: 60 },  // Kronologi
  ]

  // Add borders and formatting
  worksheet.eachRow((row, rowNumber) => {
    row.eachCell((cell) => {
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      }

      if (rowNumber > 1) {
        cell.alignment = { vertical: 'top', wrapText: true }
      }
    })
  })
}

async function createWBSSheet(worksheet: ExcelJS.Worksheet, data: LaporanExport[]) {
  // Set headers
  const headers = [
    'ID', 'Status', 'Tanggal Dibuat', 'Kategori',
    'Email Kontak', 'No HP Kontak', 'Kronologi'
  ]

  worksheet.addRow(headers)

  // Style header row
  const headerRow = worksheet.getRow(1)
  headerRow.font = { bold: true, color: { argb: 'FFFFFF' } }
  headerRow.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: '2563EB' }
  }
  headerRow.alignment = { horizontal: 'center', vertical: 'middle' }

  // Add data rows
  data.forEach(item => {
    worksheet.addRow([
      item.id,
      item.status,
      new Date(item.createdAt).toLocaleDateString('id-ID'),
      item.kategori || '-',
      item.kontakEmail || 'Anonim',
      item.kontakHp || 'Anonim',
      item.kronologi
    ])
  })

  // Set column widths and formatting
  worksheet.columns = [
    { width: 8 },   // ID
    { width: 12 },  // Status
    { width: 15 },  // Tanggal
    { width: 20 },  // Kategori
    { width: 25 },  // Email
    { width: 15 },  // HP
    { width: 60 },  // Kronologi
  ]

  // Add borders
  worksheet.eachRow((row, rowNumber) => {
    row.eachCell((cell) => {
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      }

      if (rowNumber > 1) {
        cell.alignment = { vertical: 'top', wrapText: true }
      }
    })
  })
}

async function createCOISheet(worksheet: ExcelJS.Worksheet, data: LaporanExport[]) {
  // Set headers
  const headers = [
    'ID', 'Status', 'Tanggal Dibuat', 'Pejabat Dilaporkan', 'Jenis Benturan',
    'Email Kontak', 'No HP Kontak', 'Kronologi'
  ]

  worksheet.addRow(headers)

  // Style header row
  const headerRow = worksheet.getRow(1)
  headerRow.font = { bold: true, color: { argb: 'FFFFFF' } }
  headerRow.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'EA580C' }
  }
  headerRow.alignment = { horizontal: 'center', vertical: 'middle' }

  // Add data rows
  data.forEach(item => {
    worksheet.addRow([
      item.id,
      item.status,
      new Date(item.createdAt).toLocaleDateString('id-ID'),
      item.pejabatDilaporkan || '-',
      item.jenisBenturan || '-',
      item.kontakEmail || '-',
      item.kontakHp || '-',
      item.kronologi
    ])
  })

  // Set column widths and formatting
  worksheet.columns = [
    { width: 8 },   // ID
    { width: 12 },  // Status
    { width: 15 },  // Tanggal
    { width: 25 },  // Pejabat
    { width: 20 },  // Jenis Benturan
    { width: 25 },  // Email
    { width: 15 },  // HP
    { width: 60 },  // Kronologi
  ]

  // Add borders
  worksheet.eachRow((row, rowNumber) => {
    row.eachCell((cell) => {
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      }

      if (rowNumber > 1) {
        cell.alignment = { vertical: 'top', wrapText: true }
      }
    })
  })
}

async function createPengaduanSheet(worksheet: ExcelJS.Worksheet, data: LaporanExport[]) {
  // Set headers
  const headers = [
    'ID', 'Status', 'Tanggal Dibuat', 'Jenis Pengaduan', 'Lokasi',
    'Email Kontak', 'No HP Kontak', 'Kronologi'
  ]

  worksheet.addRow(headers)

  // Style header row
  const headerRow = worksheet.getRow(1)
  headerRow.font = { bold: true, color: { argb: 'FFFFFF' } }
  headerRow.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: '059669' }
  }
  headerRow.alignment = { horizontal: 'center', vertical: 'middle' }

  // Add data rows
  data.forEach(item => {
    worksheet.addRow([
      item.id,
      item.status,
      new Date(item.createdAt).toLocaleDateString('id-ID'),
      item.jenisPengaduan || '-',
      item.lokasi || '-',
      item.kontakEmail || '-',
      item.kontakHp || '-',
      item.kronologi
    ])
  })

  // Set column widths and formatting
  worksheet.columns = [
    { width: 8 },   // ID
    { width: 12 },  // Status
    { width: 15 },  // Tanggal
    { width: 20 },  // Jenis Pengaduan
    { width: 25 },  // Lokasi
    { width: 25 },  // Email
    { width: 15 },  // HP
    { width: 60 },  // Kronologi
  ]

  // Add borders
  worksheet.eachRow((row, rowNumber) => {
    row.eachCell((cell) => {
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      }

      if (rowNumber > 1) {
        cell.alignment = { vertical: 'top', wrapText: true }
      }
    })
  })
}

export async function downloadExcel(data: LaporanExport[], filename: string = 'laporan.xlsx') {
  const buffer = await exportToExcel(data, filename)

  // Create blob and download
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  })

  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

// PDF export function (client-side)
export async function downloadPDF(data: LaporanExport[], filename: string = 'laporan.pdf') {
  // Dynamic import to avoid SSR issues
  const jsPDF = (await import('jspdf')).default
  const autoTable = (await import('jspdf-autotable')).default

  const doc = new jsPDF()
  
  // Add title
  doc.setFontSize(16)
  doc.text('Laporan WBS System', 14, 22)
  
  // Add generation date
  doc.setFontSize(10)
  doc.text(`Digenerate pada: ${new Date().toLocaleDateString('id-ID')}`, 14, 30)
  
  // Prepare table data
  const tableData = data.map(item => [
    item.id.toString(),
    item.jenis,
    item.status,
    new Date(item.createdAt).toLocaleDateString('id-ID'),
    item.kategori || item.pejabatDilaporkan || item.jenisPengaduan || '-',
    item.kronologi.substring(0, 100) + (item.kronologi.length > 100 ? '...' : '')
  ])

  // Add table
  autoTable(doc, {
    head: [['ID', 'Jenis', 'Status', 'Tanggal', 'Detail', 'Kronologi']],
    body: tableData,
    startY: 35,
    styles: { fontSize: 8 },
    headStyles: { fillColor: [66, 139, 202] },
    columnStyles: {
      0: { cellWidth: 15 },
      1: { cellWidth: 20 },
      2: { cellWidth: 20 },
      3: { cellWidth: 25 },
      4: { cellWidth: 30 },
      5: { cellWidth: 60 }
    }
  })
  
  // Save PDF
  doc.save(filename)
}

export function getExportFilename(jenis?: string, status?: string): string {
  const date = new Date().toISOString().split('T')[0]
  let filename = `laporan_${date}`
  
  if (jenis) {
    filename += `_${jenis.toLowerCase()}`
  }
  
  if (status) {
    filename += `_${status.toLowerCase()}`
  }
  
  return filename
}
