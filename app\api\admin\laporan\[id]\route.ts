import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { checkAccess, ReportType } from "@/lib/rbac"
import { UserRole } from "@prisma/client"
import { isValidUUID6 } from "@/lib/uuid-generator"

/**
 * GET /api/admin/laporan/[id] - Get laporan detail with RBAC check
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const userRole = session.user.role as UserRole
    const resolvedParams = await params
    const laporanId = resolvedParams.id

    // Validate UUID format
    if (!isValidUUID6(laporanId)) {
      return NextResponse.json({ error: "Invalid laporan ID format" }, { status: 400 })
    }

    // Get laporan basic info first to check type
    const laporan = await prisma.laporan.findUnique({
      where: { id: laporanId },
      select: {
        id: true,
        jenis: true,
        status: true,
        kronologi: true,
        lokasi: true,
        kategori: true,
        buktiUrl: true,
        pejabatDilaporkan: true,
        jenisBenturan: true,
        jenisPengaduan: true,
        createdAt: true,
        updatedAt: true,
      }
    })

    if (!laporan) {
      return NextResponse.json({ error: "Laporan not found" }, { status: 404 })
    }

    const reportType = laporan.jenis as ReportType

    // Check if user can view this report type at all
    const canViewList = checkAccess(userRole, reportType, "viewList")
    if (!canViewList) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Check if user can view details for this report type
    const canViewDetail = checkAccess(userRole, reportType, "viewDetail")
    
    if (!canViewDetail) {
      // Return limited data for users who can only view list (like ADMIN_WBS)
      return NextResponse.json({
        success: true,
        data: {
          ...laporan,
          limitedAccess: true,
          message: "You can only view basic information for this report type"
        }
      })
    }

    // User can view full details - get complete laporan data
    const fullLaporan = await prisma.laporan.findUnique({
      where: { id: laporanId },
      include: {
        // Include contact info for full access
        kontakPelapor: true
      }
    })

    if (!fullLaporan) {
      return NextResponse.json({ error: "Laporan not found" }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: {
        ...fullLaporan,
        limitedAccess: false,
        userPermissions: {
          canView: true,
          canViewDetail: true,
          canUpdate: checkAccess(userRole, reportType, "update"),
          canDelete: checkAccess(userRole, reportType, "delete"),
        }
      }
    })

  } catch (error) {
    console.error("Error fetching laporan detail:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

/**
 * PUT /api/admin/laporan/[id] - Update specific laporan
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const userRole = session.user.role as UserRole
    const resolvedParams = await params
    const laporanId = resolvedParams.id
    const body = await request.json()

    // Validate UUID format
    if (!isValidUUID6(laporanId)) {
      return NextResponse.json({ error: "Invalid laporan ID format" }, { status: 400 })
    }

    // Get laporan to check type
    const laporan = await prisma.laporan.findUnique({
      where: { id: laporanId },
      select: { jenis: true }
    })

    if (!laporan) {
      return NextResponse.json({ error: "Laporan not found" }, { status: 404 })
    }

    const reportType = laporan.jenis as ReportType

    // Check if user can update this report type
    const canUpdate = checkAccess(userRole, reportType, "update")

    if (!canUpdate) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Update laporan
    const updatedLaporan = await prisma.laporan.update({
      where: { id: laporanId },
      data: {
        ...body,
        updatedAt: new Date()
      }
    })

    return NextResponse.json({
      success: true,
      data: updatedLaporan,
      message: "Laporan updated successfully"
    })

  } catch (error) {
    console.error("Error updating laporan:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

/**
 * DELETE /api/admin/laporan/[id] - Delete specific laporan
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const userRole = session.user.role as UserRole
    const resolvedParams = await params
    const laporanId = resolvedParams.id

    // Validate UUID format
    if (!isValidUUID6(laporanId)) {
      return NextResponse.json({ error: "Invalid laporan ID format" }, { status: 400 })
    }

    // Get laporan to check type
    const laporan = await prisma.laporan.findUnique({
      where: { id: laporanId },
      select: { jenis: true }
    })

    if (!laporan) {
      return NextResponse.json({ error: "Laporan not found" }, { status: 404 })
    }

    const reportType = laporan.jenis as ReportType

    // Check if user can delete this report type
    const canDelete = checkAccess(userRole, reportType, "delete")

    if (!canDelete) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Delete laporan
    await prisma.laporan.delete({
      where: { id: laporanId }
    })

    return NextResponse.json({
      success: true,
      message: "Laporan deleted successfully"
    })

  } catch (error) {
    console.error("Error deleting laporan:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
