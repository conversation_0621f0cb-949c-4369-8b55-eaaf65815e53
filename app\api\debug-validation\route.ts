import { NextRequest, NextResponse } from "next/server"
import { validateLaporanData } from "@/lib/validation"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    console.log("🔍 Debug validation endpoint called")
    console.log("📋 Received data:", body)
    
    // Test the validation
    const validation = validateLaporanData(body, body.jenis)

    // Handle validation result with proper type checking
    if (validation.success === false) {
      // Validation failed
      console.error("❌ Validation failed:")
      console.error("🔍 Errors:", validation.error?.issues)

      return NextResponse.json({
        success: false,
        data: null,
        message: "Validation failed",
        errors: validation.error?.issues,
        receivedData: body
      })
    }

    // At this point, TypeScript knows validation.success is true
    console.log("✅ Validation passed")

    return NextResponse.json({
      success: true,
      data: 'data' in validation ? validation.data : body, // Safe access with fallback
      message: "Validation passed",
      errors: null,
      receivedData: body
    })
    
  } catch (error) {
    console.error("❌ Debug validation error:", error)
    return NextResponse.json({
      success: false,
      data: null,
      message: "Server error",
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
