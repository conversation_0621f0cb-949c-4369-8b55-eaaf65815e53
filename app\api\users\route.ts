import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import bcrypt from "bcryptjs"
import { UserRole } from "@prisma/client"
import { sanitizeFormData, getSecurityHeaders } from "@/lib/sanitize"
import { userSchema } from "@/lib/validation"
import { apiSuccess, apiError, ApiErrorCode } from "@/lib/api-response"

/**
 * Handler untuk mengambil daftar user (GET /api/users)
 * @param request NextRequest - request dari client
 * @returns NextResponse - daftar user atau error
 * @example
 * // Response sukses
 * {
 *   success: true,
 *   data: [...],
 *   message: "Daftar user berhasil diambil",
 *   error: null
 * }
 * // Response gagal
 * {
 *   success: false,
 *   data: null,
 *   message: "Unauthorized",
 *   error: "UNAUTHORIZED"
 * }
 */
export async function GET(request: NextRequest) {
  try {
    const securityHeaders = getSecurityHeaders();
    const session = await getServerSession(authOptions);

    // Allow access for all admin roles to view users (for their own profile at minimum)
    if (!session || !["SUPER_ADMIN", "ADMIN", "ADMIN_WBS", "ADMIN_COI", "ADMIN_PM"].includes(session.user.role)) {
      return NextResponse.json(apiError("Unauthorized", ApiErrorCode.UNAUTHORIZED), { status: 401, headers: securityHeaders });
    }

    // Pagination, filter, sort
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const search = searchParams.get("search") || "";
    const sort = searchParams.get("sort") || "createdAt";
    const order = searchParams.get("order") === "asc" ? "asc" : "desc";
    const skip = (page - 1) * limit;

    const where: any = {};
    if (search) {
      where.OR = [
        { nama: { contains: search, mode: "insensitive" } },
        { email: { contains: search, mode: "insensitive" } },
      ];
    }

    const total = await prisma.user.count({ where });
    const users = await prisma.user.findMany({
      where,
      select: {
        id: true,
        nama: true,
        email: true,
        role: true,
        createdAt: true,
      },
      orderBy: { [sort]: order },
      skip,
      take: limit,
    });

    return NextResponse.json(
      apiSuccess(users, "Daftar user berhasil diambil", {
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      }),
      { headers: securityHeaders }
    );
  } catch (error) {
    console.error("[GET /api/users]", error);
    return NextResponse.json(apiError("Terjadi kesalahan server", ApiErrorCode.INTERNAL_SERVER_ERROR), { status: 500, headers: getSecurityHeaders() });
  }
}

/**
 * Handler untuk membuat user baru (POST /api/users)
 * @param request NextRequest - request dari client, berisi data user
 * @returns NextResponse - hasil pembuatan user atau error
 * @example
 * // Response sukses
 * {
 *   success: true,
 *   data: { ...user },
 *   message: "User berhasil dibuat",
 *   error: null
 * }
 * // Response gagal
 * {
 *   success: false,
 *   data: null,
 *   message: "Email sudah digunakan",
 *   error: "EMAIL_EXISTS"
 * }
 */
export async function POST(request: NextRequest) {
  try {
    const securityHeaders = getSecurityHeaders()
    const session = await getServerSession(authOptions)

    // Only Super Admin and Admin can create users
    if (!session || !["SUPER_ADMIN", "ADMIN"].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, data: null, message: "Unauthorized", error: "UNAUTHORIZED" },
        { status: 401, headers: securityHeaders }
      )
    }

    const body = await request.json()

    // Sanitize input data
    const sanitizedData = sanitizeFormData(body)

    // Validate with schema
    const validation = userSchema.safeParse(sanitizedData)
    if (!validation.success) {
      const errorMessage = validation.error.issues[0]?.message || "Data tidak valid"
      return NextResponse.json(
        { success: false, data: null, message: errorMessage, error: "VALIDATION_ERROR" },
        { status: 400, headers: securityHeaders }
      )
    }

    const { nama, email, password, role } = validation.data

    // Cek apakah email sudah ada
    const existingUser = await prisma.user.findUnique({
      where: { email },
    })

    if (existingUser) {
      return NextResponse.json(
        { success: false, data: null, message: "Email sudah digunakan", error: "EMAIL_EXISTS" },
        { status: 400, headers: securityHeaders }
      )
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12)

    // Buat user baru
    const user = await prisma.user.create({
      data: {
        nama,
        email,
        password: hashedPassword,
        role: role as UserRole,
      },
      select: {
        id: true,
        nama: true,
        email: true,
        role: true,
        createdAt: true,
      },
    })

    return NextResponse.json({
      success: true,
      data: user,
      message: "User berhasil dibuat",
      error: null
    }, { headers: securityHeaders })

  } catch (error) {
    console.error("[POST /api/users]", error)
    return NextResponse.json(
      { success: false, data: null, message: "Terjadi kesalahan server", error: "INTERNAL_SERVER_ERROR" },
      { status: 500, headers: getSecurityHeaders() }
    )
  }
}
