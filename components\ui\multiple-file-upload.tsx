"use client"

import React, { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Upload, X, FileText, Image, File } from "lucide-react"
import { toast } from "sonner"

interface MultipleFileUploadProps {
  onFilesChange: (files: File[]) => void
  selectedFiles: File[]
  accept?: string
  maxFiles?: number
  maxSize?: number // in bytes
  className?: string
}

export function MultipleFileUpload({
  onFilesChange,
  selectedFiles,
  accept = ".pdf,.doc,.docx,.jpg,.jpeg,.png",
  maxFiles = 5,
  maxSize = 10 * 1024 * 1024, // 10MB
  className
}: MultipleFileUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    
    // Check total files limit
    if (selectedFiles.length + files.length > maxFiles) {
      toast.error(`Maksimal ${maxFiles} file dapat diupload`)
      return
    }

    // Validate each file
    const validFiles: File[] = []
    for (const file of files) {
      // Check file size
      if (file.size > maxSize) {
        toast.error(`File ${file.name} terlalu besar. Maksimal ${Math.round(maxSize / 1024 / 1024)}MB`)
        continue
      }

      // Check if file already exists
      if (selectedFiles.some(f => f.name === file.name && f.size === file.size)) {
        toast.error(`File ${file.name} sudah dipilih`)
        continue
      }

      validFiles.push(file)
    }

    if (validFiles.length > 0) {
      onFilesChange([...selectedFiles, ...validFiles])
      toast.success(`${validFiles.length} file berhasil ditambahkan`)
    }

    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  const removeFile = (index: number) => {
    const newFiles = selectedFiles.filter((_, i) => i !== index)
    onFilesChange(newFiles)
    toast.success("File berhasil dihapus")
  }

  const getFileIcon = (file: File) => {
    if (file.type.startsWith("image/")) {
      return <Image className="h-4 w-4 text-blue-500" />
    } else if (file.type === "application/pdf") {
      return <FileText className="h-4 w-4 text-red-500" />
    } else {
      return <File className="h-4 w-4 text-gray-500" />
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  return (
    <div className={className}>
      {/* Upload Area */}
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
        <Upload className="mx-auto h-12 w-12 text-gray-400" />
        <div className="mt-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            disabled={selectedFiles.length >= maxFiles}
          >
            {selectedFiles.length >= maxFiles ? "Maksimal file tercapai" : "Pilih File"}
          </Button>
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept={accept}
            onChange={handleFileSelect}
            className="hidden"
          />
        </div>
        <p className="mt-2 text-sm text-gray-500">
          PDF, DOC, DOCX, JPG, PNG (max {Math.round(maxSize / 1024 / 1024)}MB per file)
        </p>
        <p className="text-xs text-gray-400">
          Maksimal {maxFiles} file • {selectedFiles.length}/{maxFiles} dipilih
        </p>
      </div>

      {/* Selected Files List */}
      {selectedFiles.length > 0 && (
        <div className="mt-4 space-y-2">
          <h4 className="text-sm font-medium text-gray-700">File yang dipilih:</h4>
          {selectedFiles.map((file, index) => (
            <div
              key={`${file.name}-${index}`}
              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
            >
              <div className="flex items-center space-x-3">
                {getFileIcon(file)}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {file.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(file.size)}
                  </p>
                </div>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => removeFile(index)}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* Upload Guidelines */}
      <div className="mt-4 p-3 bg-blue-50 rounded-lg">
        <h5 className="text-sm font-medium text-blue-800 mb-1">Panduan Upload:</h5>
        <ul className="text-xs text-blue-700 space-y-1">
          <li>• Maksimal {maxFiles} file dapat diupload</li>
          <li>• Ukuran maksimal per file: {Math.round(maxSize / 1024 / 1024)}MB</li>
          <li>• Format yang didukung: PDF, DOC, DOCX, JPG, PNG</li>
          <li>• File dengan nama yang sama akan ditolak</li>
        </ul>
      </div>
    </div>
  )
}
