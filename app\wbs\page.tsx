"use client"

import { useState, useRef } from "react"
import { use<PERSON>outer } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import <PERSON><PERSON>tcha from "@hcaptcha/react-hcaptcha"
import { wbsFormSchema, type WBSFormData } from "@/lib/validation"
import { sanitizeFormInput } from "@/lib/sanitize-client"
import { Button } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Navbar } from "@/components/layout/navbar"
import { toast } from "sonner"
import { ArrowLef<PERSON>, Shield, Upload } from "lucide-react"
import Link from "next/link"
import { MultipleFileUpload } from "@/components/ui/multiple-file-upload"

const kategoriOptions = [
  "Korupsi",
  "Penyalahgunaan Wewenang", 
  "Nepotisme",
  "Gratifikasi",
  "Pelanggaran Etika",
  "Penyalahgunaan Anggaran",
  "Lainnya"
]

export default function WBSPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [captchaToken, setCaptchaToken] = useState<string>("")
  const captchaRef = useRef<HCaptcha>(null)

  const form = useForm<WBSFormData>({
    resolver: zodResolver(wbsFormSchema),
    defaultValues: {
      kategori: "",
      kronologi: "",
      buktiFile: null,
      kontakEmail: "",
      kontakHp: "",
      hcaptchaToken: "",
    },
  })

  const onSubmit = async (data: WBSFormData) => {
    if (!captchaToken) {
      toast.error("Silakan selesaikan captcha terlebih dahulu")
      return
    }

    setIsSubmitting(true)

    try {
      const formData = new FormData()

      // Add jenis field for WBS
      formData.append("jenis", "WBS")

      // Sanitize input data
      const sanitizedData = {
        kategori: sanitizeFormInput(data.kategori),
        kronologi: sanitizeFormInput(data.kronologi),
        kontakEmail: data.kontakEmail ? sanitizeFormInput(data.kontakEmail) : "",
        kontakHp: data.kontakHp ? sanitizeFormInput(data.kontakHp) : "",
        hcaptchaToken: captchaToken,
      }

      // Append form data
      Object.entries(sanitizedData).forEach(([key, value]) => {
        formData.append(key, value)
      })

      // Append multiple files if exist
      if (selectedFiles.length > 0) {
        selectedFiles.forEach((file) => {
          formData.append("buktiFile", file)
        })
      }

      // Validate required fields before sending
      const requiredFields = ['jenis', 'kategori', 'kronologi', 'hcaptchaToken']
      const missingFields = requiredFields.filter(field => !formData.get(field))

      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(', ')}`)
      }

      const response = await fetch("/api/laporan", {
        method: "POST",
        body: formData,
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || "Gagal mengirim laporan")
      }

      // Check if result has the expected structure
      const laporanId = result.data?.id || result.id

      if (!laporanId) {
        throw new Error("ID laporan tidak ditemukan dalam response")
      }

      toast.success("Laporan WBS berhasil dikirim!", {
        description: `ID Laporan: ${laporanId}. Simpan ID ini untuk referensi.`
      })

      // Reset form
      form.reset()
      setSelectedFiles([])
      setCaptchaToken("")
      captchaRef.current?.resetCaptcha()

      // Redirect ke halaman sukses
      router.push(`/success?id=${laporanId}&jenis=WBS`)

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Gagal mengirim laporan"
      toast.error("Gagal mengirim laporan WBS", {
        description: errorMessage
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCaptchaVerify = (token: string) => {
    setCaptchaToken(token)
    form.setValue("hcaptchaToken", token)
    form.clearErrors("hcaptchaToken")
  }

  const handleCaptchaExpire = () => {
    setCaptchaToken("")
    form.setValue("hcaptchaToken", "")
  }



  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link href="/" className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4">
            <ArrowLeft className="h-4 w-4 mr-1" />
            Kembali ke Beranda
          </Link>
          
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
              <Shield className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Whistleblowing System</h1>
              <p className="text-gray-600">Laporkan dugaan pelanggaran secara anonim</p>
            </div>
          </div>
        </div>

        {/* Form */}
        <Card>
          <CardHeader>
            <CardTitle>Form Pelaporan WBS</CardTitle>
            <CardDescription>
              Semua informasi yang Anda berikan akan dijaga kerahasiaannya. 
              Identitas pelapor tidak akan disimpan dalam sistem.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {/* Kategori */}
                <FormField
                  control={form.control}
                  name="kategori"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Kategori Pelanggaran *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih kategori pelanggaran" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {kategoriOptions.map((kategori) => (
                            <SelectItem key={kategori} value={kategori}>
                              {kategori}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Pilih kategori yang paling sesuai dengan pelanggaran yang dilaporkan
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Kronologi */}
                <FormField
                  control={form.control}
                  name="kronologi"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Kronologi Pelanggaran *</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Jelaskan secara detail kronologi pelanggaran yang terjadi, termasuk waktu, tempat, dan pihak yang terlibat..."
                          className="min-h-32"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Semakin detail informasi yang diberikan, semakin mudah untuk ditindaklanjuti
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Multiple File Upload */}
                <div className="space-y-2">
                  <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Bukti Pendukung *
                  </label>
                  <MultipleFileUpload
                    selectedFiles={selectedFiles}
                    onFilesChange={setSelectedFiles}
                    maxFiles={5}
                    maxSize={10 * 1024 * 1024} // 10MB
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                  />
                  <p className="text-sm text-muted-foreground">
                    Upload dokumen, foto, atau file lain yang mendukung laporan Anda. Wajib untuk laporan WBS.
                  </p>
                  {selectedFiles.length === 0 && (
                    <p className="text-sm text-red-600">
                      Minimal 1 file bukti pendukung harus diupload untuk laporan WBS
                    </p>
                  )}
                </div>

                {/* Kontak Email */}
                <FormField
                  control={form.control}
                  name="kontakEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Kontak (Opsional)</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Email untuk komunikasi tindak lanjut jika diperlukan. Kerahasiaan tetap terjaga.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Kontak HP */}
                <FormField
                  control={form.control}
                  name="kontakHp"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nomor HP (Opsional)</FormLabel>
                      <FormControl>
                        <Input
                          type="tel"
                          placeholder="08xxxxxxxxxx"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Nomor HP untuk komunikasi tindak lanjut jika diperlukan. Kerahasiaan tetap terjaga.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Captcha */}
                <FormField
                  control={form.control}
                  name="hcaptchaToken"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Verifikasi Keamanan *</FormLabel>
                      <FormControl>
                        <div className="flex justify-center">
                          <HCaptcha
                            ref={captchaRef}
                            sitekey={process.env.NEXT_PUBLIC_HCAPTCHA_SITE_KEY!}
                            onVerify={handleCaptchaVerify}
                            onExpire={handleCaptchaExpire}
                          />
                        </div>
                      </FormControl>
                      <FormDescription>
                        Selesaikan captcha untuk memverifikasi bahwa Anda bukan robot
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Submit Button */}
                <div className="flex flex-col sm:flex-row gap-4 pt-6">
                  <Link href="/" className="flex-1">
                    <Button type="button" variant="outline" className="w-full">
                      Batal
                    </Button>
                  </Link>
                  <Button type="submit" disabled={isSubmitting || !captchaToken} className="flex-1">
                    {isSubmitting ? "Mengirim..." : (!captchaToken ? "Selesaikan Captcha untuk Melanjutkan" : "Kirim Laporan")}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* Disclaimer */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-800 mb-2">
            Perlindungan Pelapor
          </h3>
          <ul className="text-xs text-blue-700 space-y-1">
            <li>• Identitas pelapor dijamin kerahasiaannya</li>
            <li>• Tidak ada data identitas yang disimpan secara permanen</li>
            <li>• Laporan akan diproses secara profesional dan objektif</li>
            <li>• Pelapor dilindungi dari tindakan balasan</li>
          </ul>
        </div>
      </div>
    </div>
  )
}