import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { JenisLaporan } from "@prisma/client"
import { uploadFile } from "@/lib/file-upload"
import { validateRequest } from "@/lib/csrf"
import { verifyHCaptcha } from "@/lib/hcaptcha"
import { sanitizeFormData, getSecurityHeaders } from "@/lib/sanitize"
import { validateLaporanData } from "@/lib/validation"
import { sendLaporanConfirmation } from "@/lib/email"
import { apiSuccess, apiError, ApiErrorCode } from "@/lib/api-response"
import { generateUniqueUUID6 } from "@/lib/uuid-generator"

/**
 * Handler untuk membuat laporan baru (POST /api/laporan)
 * @param request NextRequest - request dari client, berisi formData laporan
 * @returns NextResponse - hasil pembuatan laporan atau error
 * @example
 * // Response sukses
 * {
 *   success: true,
 *   data: { id: 1 },
 *   message: "Laporan berhasil dikirim",
 *   error: null
 * }
 * // Response gagal
 * {
 *   success: false,
 *   data: null,
 *   message: "Data tidak valid",
 *   error: "VALIDATION_ERROR"
 * }
 */
export async function POST(request: NextRequest) {
  try {
    // ===== ENHANCED API DEBUG LOGGING =====
    console.group("🚀 [API /api/laporan] POST Request Processing")
    console.log("⏰ Request timestamp:", new Date().toISOString())

    // Add security headers
    const securityHeaders = getSecurityHeaders()

    // Log request headers for debugging
    console.log("📋 Request headers:", {
      origin: request.headers.get("origin"),
      host: request.headers.get("host"),
      userAgent: request.headers.get("user-agent"),
      contentType: request.headers.get("content-type"),
      contentLength: request.headers.get("content-length")
    })

    // Validate request (CSRF protection for authenticated requests)
    // Note: Public forms don't need CSRF, but we validate origin
    const origin = request.headers.get("origin")
    const host = request.headers.get("host")

    if (origin && host) {
      const originUrl = new URL(origin)
      if (originUrl.host !== host) {
        console.error("❌ Invalid origin detected:", { origin, host })
        return NextResponse.json(
          { success: false, data: null, message: "Invalid origin", error: "INVALID_ORIGIN" },
          { status: 403, headers: securityHeaders }
        )
      }
    }

    console.log("✅ Origin validation passed")

    // Parse FormData
    console.log("📦 Parsing FormData...")
    const formData = await request.formData()

    // ===== DETAILED FORM DATA LOGGING =====
    console.log("📋 Received FormData entries:")
    const receivedData: Record<string, any> = {}
    const fileData: Array<{name: string, size: number, type: string}> = []

    for (const pair of formData.entries()) {
      const key = pair[0]
      const value = pair[1]

      if (value instanceof File) {
        fileData.push({
          name: value.name,
          size: value.size,
          type: value.type
        })
        console.log(`  📎 ${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`)
      } else {
        receivedData[key] = value
        console.log(`  📝 ${key}: "${value}" (${typeof value})`)
      }
    }

    console.log("📊 Summary of received data:", {
      textFields: Object.keys(receivedData).length,
      files: fileData.length,
      totalFormDataEntries: Array.from(formData.entries()).length
    })

    // Extract and sanitize form data
    const rawData = {
      jenis: formData.get("jenis") as string,
      kronologi: formData.get("kronologi") as string,
      kontakEmail: formData.get("kontakEmail") as string | null,
      kontakHp: formData.get("kontakHp") as string | null,
      hcaptchaToken: formData.get("hcaptchaToken") as string | null,
      kategori: formData.get("kategori") as string | null,
      pejabatDilaporkan: formData.get("pejabatDilaporkan") as string | null,
      jenisBenturan: formData.get("jenisBenturan") as string | null,
      jenisPengaduan: formData.get("jenisPengaduan") as string | null,
      lokasi: formData.get("lokasi") as string | null,
    }





    // ===== DATA SANITIZATION =====
    console.log("🧹 Sanitizing form data...")
    const sanitizedData = sanitizeFormData(rawData)

    // ===== CLEAN NULL VALUES FOR VALIDATION =====
    console.log("🧽 Cleaning null values for validation...")
    const cleanedData = Object.fromEntries(
      Object.entries(sanitizedData).map(([key, value]) => [
        key,
        value === null ? undefined : value
      ])
    )

    console.log("✨ Sanitized data:", sanitizedData)
    console.log("🧽 Cleaned data for validation:", cleanedData)

    // ===== PRISMA SCHEMA COMPARISON =====
    console.log("🔍 Prisma Schema vs Input Data Comparison:")
    console.table({
      "jenis (JenisLaporan)": {
        required: true,
        received: sanitizedData.jenis,
        valid: ["WBS", "COI", "PENGADUAN"].includes(sanitizedData.jenis),
        status: sanitizedData.jenis === "COI" ? "✅" : "❌"
      },
      "kronologi (Text)": {
        required: true,
        received: sanitizedData.kronologi ? `"${sanitizedData.kronologi.substring(0, 50)}..."` : "null",
        valid: !!sanitizedData.kronologi && sanitizedData.kronologi.trim().length > 0,
        status: sanitizedData.kronologi ? "✅" : "❌"
      },
      "pejabatDilaporkan (String?)": {
        required: "COI only",
        received: sanitizedData.pejabatDilaporkan || "null",
        valid: sanitizedData.jenis !== "COI" || !!sanitizedData.pejabatDilaporkan,
        status: sanitizedData.jenis === "COI" && sanitizedData.pejabatDilaporkan ? "✅" : sanitizedData.jenis !== "COI" ? "N/A" : "❌"
      },
      "jenisBenturan (String?)": {
        required: "COI only",
        received: sanitizedData.jenisBenturan || "null",
        valid: sanitizedData.jenis !== "COI" || !!sanitizedData.jenisBenturan,
        status: sanitizedData.jenis === "COI" && sanitizedData.jenisBenturan ? "✅" : sanitizedData.jenis !== "COI" ? "N/A" : "❌"
      },
      "hcaptchaToken": {
        required: true,
        received: sanitizedData.hcaptchaToken ? "Present" : "null",
        valid: !!sanitizedData.hcaptchaToken,
        status: sanitizedData.hcaptchaToken ? "✅" : "❌"
      }
    })

    const jenis = cleanedData.jenis
    const kronologi = cleanedData.kronologi
    const kontakEmail = cleanedData.kontakEmail
    const kontakHp = cleanedData.kontakHp
    const hcaptchaToken = cleanedData.hcaptchaToken

    // ===== SPECIFIC FIELD VALIDATION =====
    console.log("🔍 Validating specific fields...")

    // Validate kronologi to prevent error messages from being submitted
    if (!kronologi || kronologi.trim() === "" ||
        kronologi.includes("Error:") ||
        kronologi.includes("Gagal mengirim") ||
        kronologi.includes("at onSubmit")) {
      console.error("❌ Invalid kronologi:", kronologi)
      return NextResponse.json(
        { success: false, data: null, message: "Kronologi harus diisi dengan deskripsi yang valid", error: "INVALID_KRONOLOGI" },
        { status: 400, headers: securityHeaders }
      )
    }

    // Validate COI-specific fields
    if (jenis === "COI") {
      const pejabatDilaporkan = cleanedData.pejabatDilaporkan
      const jenisBenturan = cleanedData.jenisBenturan

      console.log("🔍 COI-specific validation:", {
        pejabatDilaporkan: pejabatDilaporkan,
        pejabatDilaporkanLength: pejabatDilaporkan?.length || 0,
        pejabatDilaporkanValid: pejabatDilaporkan && pejabatDilaporkan.trim().length > 0 && pejabatDilaporkan.trim() !== ",m,",
        jenisBenturan: jenisBenturan,
        jenisBenturanValid: jenisBenturan && jenisBenturan.trim().length > 0
      })

      if (!pejabatDilaporkan || pejabatDilaporkan.trim().length === 0 || pejabatDilaporkan.trim() === ",m,") {
        console.error("❌ Invalid pejabatDilaporkan for COI:", pejabatDilaporkan)
        return NextResponse.json(
          { success: false, data: null, message: "Nama pejabat yang dilaporkan harus diisi dengan nama yang valid", error: "INVALID_PEJABAT" },
          { status: 400, headers: securityHeaders }
        )
      }

      if (!jenisBenturan || jenisBenturan.trim().length === 0) {
        console.error("❌ Invalid jenisBenturan for COI:", jenisBenturan)
        return NextResponse.json(
          { success: false, data: null, message: "Jenis benturan kepentingan harus dipilih", error: "INVALID_JENIS_BENTURAN" },
          { status: 400, headers: securityHeaders }
        )
      }
    }

    // Get multiple files from formData
    const buktiFiles: File[] = []
    const allFiles = formData.getAll("buktiFile") as File[]

    // Filter out empty files and add valid files
    allFiles.forEach(file => {
      if (file && file.size > 0) {
        buktiFiles.push(file)
      }
    })

    // For backward compatibility, also check single file
    const singleFile = formData.get("buktiFile") as File | null
    if (singleFile && singleFile.size > 0 && !buktiFiles.some(f => f.name === singleFile.name)) {
      buktiFiles.push(singleFile)
    }

    // ===== MANUAL FIELD VALIDATION FIRST =====
    console.log("🔍 Manual field validation before Zod...")
    const manualValidation = {
      jenis: {
        value: cleanedData.jenis,
        valid: ["WBS", "COI", "PENGADUAN"].includes(cleanedData.jenis),
        required: true
      },
      kronologi: {
        value: cleanedData.kronologi?.substring(0, 50) + "...",
        valid: !!cleanedData.kronologi && cleanedData.kronologi.trim().length > 0,
        required: true
      },
      pejabatDilaporkan: {
        value: cleanedData.pejabatDilaporkan,
        valid: cleanedData.jenis !== "COI" || (!!cleanedData.pejabatDilaporkan && cleanedData.pejabatDilaporkan.trim().length > 0),
        required: cleanedData.jenis === "COI"
      },
      jenisBenturan: {
        value: cleanedData.jenisBenturan,
        valid: cleanedData.jenis !== "COI" || (!!cleanedData.jenisBenturan && cleanedData.jenisBenturan.trim().length > 0),
        required: cleanedData.jenis === "COI"
      },
      hcaptchaToken: {
        value: cleanedData.hcaptchaToken ? "Present" : "Missing",
        valid: !!cleanedData.hcaptchaToken,
        required: true
      }
    }

    console.table(manualValidation)

    // Check for manual validation failures
    const failedFields = Object.entries(manualValidation).filter(([key, validation]) => !validation.valid)
    if (failedFields.length > 0) {
      console.error("❌ Manual validation failed for fields:", failedFields.map(([key, val]) => `${key}: ${val.value}`))
      const firstFailedField = failedFields[0]
      return NextResponse.json(
        { success: false, data: null, message: `Field '${firstFailedField[0]}' is invalid: ${firstFailedField[1].value}`, error: "MANUAL_VALIDATION_ERROR" },
        { status: 400, headers: securityHeaders }
      )
    }

    // ===== SCHEMA VALIDATION =====
    console.log("🔍 Running Zod schema validation...")
    const validation = validateLaporanData(cleanedData, jenis as "WBS" | "COI" | "PENGADUAN")

    if (!validation.success) {
      console.error("❌ Schema validation failed!")
      console.error("🔍 Validation errors:", validation.error?.issues)
      console.error("📋 Failed data:", cleanedData)

      // Log each validation error in detail
      validation.error?.issues?.forEach((issue, index) => {
        console.error(`❌ Validation Error ${index + 1}:`, {
          path: (issue as any).path || 'unknown',
          message: issue.message,
          code: (issue as any).code || 'unknown',
          received: (issue as any).received || 'N/A'
        })
      })

      const errorMessage = validation.error?.issues?.[0]?.message || "Data tidak valid"
      console.error("🚨 Returning validation error:", errorMessage)

      return NextResponse.json(
        { success: false, data: null, message: errorMessage, error: "VALIDATION_ERROR" },
        { status: 400, headers: securityHeaders }
      )
    }

    console.log("✅ Schema validation passed")

    // Validasi hCaptcha
    if (!hcaptchaToken) {
      return NextResponse.json(
        { success: false, data: null, message: "Captcha harus diverifikasi", error: "HCAPTCHA_REQUIRED" },
        { status: 400, headers: securityHeaders }
      )
    }

    const captchaResult = await verifyHCaptcha(hcaptchaToken)
    console.log("🔐 Captcha verification result:", captchaResult)
    if (!captchaResult.success) {
      console.log("❌ Captcha verification failed:", captchaResult.error)
      return NextResponse.json(
        { success: false, data: null, message: captchaResult.error || "Verifikasi captcha gagal", error: "HCAPTCHA_INVALID" },
        { status: 400, headers: securityHeaders }
      )
    }

    // Validasi jenis laporan
    if (!Object.values(JenisLaporan).includes(jenis as JenisLaporan)) {
      return NextResponse.json(
        { success: false, data: null, message: "Jenis laporan tidak valid", error: "INVALID_JENIS" },
        { status: 400, headers: securityHeaders }
      )
    }

    let buktiUrls: string[] = []

    // Validasi file upload wajib hanya untuk WBS
    if (jenis === "WBS" && buktiFiles.length === 0) {
      return NextResponse.json(
        { success: false, data: null, message: "Bukti pendukung harus diupload untuk laporan WBS", error: "BUKTI_REQUIRED" },
        { status: 400, headers: securityHeaders }
      )
    }

    // Validasi maksimal 5 file
    if (buktiFiles.length > 5) {
      return NextResponse.json(
        { success: false, data: null, message: "Maksimal 5 file dapat diupload", error: "TOO_MANY_FILES" },
        { status: 400, headers: securityHeaders }
      )
    }

    // Handle multiple file uploads
    if (buktiFiles.length > 0) {
      for (const file of buktiFiles) {
        const uploadResult = await uploadFile(file, {
          maxSize: 10 * 1024 * 1024, // 10MB per file
          allowedTypes: [
            "image/jpeg",
            "image/png",
            "image/jpg",
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
          ]
        })

        if (!uploadResult.success) {
          return NextResponse.json(
            { success: false, data: null, message: `Error uploading ${file.name}: ${uploadResult.error}`, error: "UPLOAD_ERROR" },
            { status: 400, headers: securityHeaders }
          )
        }

        if (uploadResult.url) {
          buktiUrls.push(uploadResult.url)
        }
      }
    }

    // Siapkan data laporan berdasarkan jenis
    let laporanData: any = {
      jenis: jenis as JenisLaporan,
      kronologi,
      buktiUrl: buktiUrls.length > 0 ? buktiUrls[0] : null, // For backward compatibility
      buktiUrls: buktiUrls.length > 0 ? JSON.stringify(buktiUrls) : null, // JSON string for multiple files
    }

    // Tambahkan field spesifik berdasarkan jenis laporan (sudah disanitasi)
    if (jenis === "WBS") {
      laporanData.kategori = cleanedData.kategori
    } else if (jenis === "COI") {
      laporanData.pejabatDilaporkan = cleanedData.pejabatDilaporkan
      laporanData.jenisBenturan = cleanedData.jenisBenturan
    } else if (jenis === "PENGADUAN") {
      laporanData.jenisPengaduan = cleanedData.jenisPengaduan
      laporanData.lokasi = cleanedData.lokasi
    }



    // ===== DATABASE OPERATIONS =====
    console.log("💾 Saving to database...")

    // Generate unique UUID for laporan
    const laporanId = await generateUniqueUUID6(async (uuid) => {
      const existing = await prisma.laporan.findUnique({ where: { id: uuid } })
      return !!existing
    })

    console.log("🆔 Generated laporan UUID:", laporanId)

    // Add ID to laporan data
    const finalLaporanData = {
      ...laporanData,
      id: laporanId
    }

    console.log("📊 Final laporan data to be saved:", finalLaporanData)

    const laporan = await prisma.laporan.create({
      data: finalLaporanData,
    })

    console.log("✅ Laporan saved successfully:", {
      id: laporan.id,
      jenis: laporan.jenis,
      createdAt: laporan.createdAt
    })

    // Simpan kontak pelapor jika ada
    if ((kontakEmail && kontakEmail.trim() !== "") || (kontakHp && kontakHp.trim() !== "")) {
      console.log("📞 Saving contact information...")

      // Generate unique UUID for kontak pelapor
      const kontakId = await generateUniqueUUID6(async (uuid) => {
        const existing = await prisma.kontakPelapor.findUnique({ where: { id: uuid } })
        return !!existing
      })

      const kontakData = {
        id: kontakId,
        laporanId: laporan.id,
        email: kontakEmail && kontakEmail.trim() !== "" ? kontakEmail : null,
        noHp: kontakHp && kontakHp.trim() !== "" ? kontakHp : null,
      }
      console.log("📋 Contact data:", kontakData)

      await prisma.kontakPelapor.create({
        data: kontakData,
      })
      console.log("✅ Contact information saved with ID:", kontakId)
    } else {
      console.log("ℹ️ No contact information provided")
    }

    // Send email confirmation if email is provided
    if (kontakEmail && kontakEmail.trim() !== '') {
      try {
        await sendLaporanConfirmation(kontakEmail, laporan.id, jenis)
        console.log(`Confirmation email sent to ${kontakEmail} for laporan #${laporan.id}`)
      } catch (error) {
        console.error('Failed to send confirmation email:', error)
        // Don't fail the request if email fails
      }
    }

    // ===== SUCCESS RESPONSE =====
    const successResponse = {
      success: true,
      data: { id: laporan.id },
      message: "Laporan berhasil dikirim",
      error: null
    }

    console.log("🎉 Sending success response:", successResponse)
    console.groupEnd()

    return NextResponse.json(successResponse, { headers: securityHeaders })

  } catch (error) {
    // Logging error hanya di server
    console.error("❌ [POST /api/laporan] Error:", error)
    if (error instanceof Error) {
      console.error("❌ Error stack:", error.stack)
    }
    // Debug: tampilkan error message dan error object
    console.error("[API /api/laporan] Error message:", error instanceof Error ? error.message : error)

    // Return more specific error message
    const errorMessage = error instanceof Error ? error.message : "Terjadi kesalahan server"
    return NextResponse.json(
      { success: false, data: null, message: errorMessage, error: "INTERNAL_SERVER_ERROR" },
      { status: 500, headers: getSecurityHeaders() }
    )
  }
}

/**
 * Handler untuk mengambil daftar laporan (GET /api/laporan)
 * @param request NextRequest - request dari client, bisa berisi query string (page, limit, jenis, status, search)
 * @returns NextResponse - daftar laporan dengan pagination
 * @example
 * // Response sukses
 * {
 *   success: true,
 *   data: [...],
 *   message: "Daftar laporan berhasil diambil",
 *   error: null,
 *   pagination: { page: 1, limit: 10, total: 100, totalPages: 10 }
 * }
 * // Response gagal
 * {
 *   success: false,
 *   data: null,
 *   message: "Terjadi kesalahan server",
 *   error: "INTERNAL_SERVER_ERROR"
 * }
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const jenis = searchParams.get("jenis");
    const status = searchParams.get("status");
    const search = searchParams.get("search") || "";
    const sort = searchParams.get("sort") || "createdAt";
    const order = searchParams.get("order") === "asc" ? "asc" : "desc";
    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    if (jenis && Object.values(JenisLaporan).includes(jenis as JenisLaporan)) {
      where.jenis = jenis as JenisLaporan;
    }
    if (status) {
      where.status = status;
    }
    if (search) {
      where.OR = [
        { kronologi: { contains: search, mode: "insensitive" } },
        { kategori: { contains: search, mode: "insensitive" } },
        { pejabatDilaporkan: { contains: search, mode: "insensitive" } },
        { lokasi: { contains: search, mode: "insensitive" } },
      ];
    }

    const total = await prisma.laporan.count({ where });
    const laporan = await prisma.laporan.findMany({
      where,
      include: {
        kontakPelapor: true,
      },
      orderBy: { [sort]: order },
      skip,
      take: limit,
    });

    return NextResponse.json(
      apiSuccess(laporan, "Daftar laporan berhasil diambil", {
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      })
    );
  } catch (error) {
    console.error("[GET /api/laporan]", error);
    return NextResponse.json(apiError("Terjadi kesalahan server", ApiErrorCode.INTERNAL_SERVER_ERROR), { status: 500 });
  }
}
