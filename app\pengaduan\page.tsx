"use client"

import { useState, useRef } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import <PERSON><PERSON>tch<PERSON> from "@hcaptcha/react-hcaptcha"
import { pengaduanFormSchema, type PengaduanFormData } from "@/lib/validation"
import { sanitizeFormInput } from "@/lib/sanitize-client"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Navbar } from "@/components/layout/navbar"
import { toast } from "sonner"
import { <PERSON><PERSON><PERSON><PERSON>, MessageSquare, Upload } from "lucide-react"
import Link from "next/link"
import { MultipleFileUpload } from "@/components/ui/multiple-file-upload"

// Using imported schema from validation.ts

const jenisPengaduanOptions = [
  "Pelayanan Publik",
  "Kebersihan",
  "Keamanan",
  "Lainnya"
]

export default function PengaduanPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [captchaToken, setCaptchaToken] = useState<string>("")
  const captchaRef = useRef<HCaptcha>(null)

  const form = useForm<PengaduanFormData>({
    resolver: zodResolver(pengaduanFormSchema),
    defaultValues: {
      jenisPengaduan: "",
      lokasi: "",
      kronologi: "",
      buktiFile: null,
      kontakEmail: "",
      kontakHp: "",
      hcaptchaToken: "",
    },
  })

  const onSubmit = async (values: PengaduanFormData) => {
    setIsSubmitting(true)

    try {
      // Validasi captcha token
      if (!values.hcaptchaToken) {
        toast.error("Captcha harus diverifikasi")
        setIsSubmitting(false)
        return
      }

      const formData = new FormData()

      // Required fields for PENGADUAN
      formData.append("jenis", "PENGADUAN")
      formData.append("jenisPengaduan", values.jenisPengaduan)
      formData.append("lokasi", values.lokasi)
      formData.append("kronologi", values.kronologi)
      formData.append("hcaptchaToken", values.hcaptchaToken)

      // Optional contact fields
      if (values.kontakEmail) {
        formData.append("kontakEmail", values.kontakEmail)
      }
      if (values.kontakHp) {
        formData.append("kontakHp", values.kontakHp)
      }

      // File attachments
      if (selectedFiles.length > 0) {
        selectedFiles.forEach((file) => {
          formData.append("buktiFile", file)
        })
      }

      // Validate required fields before sending
      const requiredFields = ['jenis', 'jenisPengaduan', 'lokasi', 'kronologi', 'hcaptchaToken']
      const missingFields = requiredFields.filter(field => !formData.get(field))

      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(', ')}`)
      }

      const response = await fetch("/api/laporan", {
        method: "POST",
        body: formData,
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || "Gagal mengirim pengaduan")
      }

      // Check if result has the expected structure
      const laporanId = result.data?.id || result.id

      if (!laporanId) {
        throw new Error("ID laporan tidak ditemukan dalam response")
      }

      toast.success("Pengaduan berhasil dikirim!", {
        description: `ID Pengaduan: ${laporanId}. Simpan ID ini untuk referensi.`
      })

      // Reset form
      form.reset()
      setSelectedFiles([])
      setCaptchaToken("")
      captchaRef.current?.resetCaptcha()

      // Redirect ke halaman sukses dengan ID laporan
      router.push(`/success?id=${laporanId}&jenis=PENGADUAN`)

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Gagal mengirim pengaduan"
      toast.error("Gagal mengirim pengaduan", {
        description: errorMessage
      })
    } finally {
      setIsSubmitting(false)
    }
  }



  const handleCaptchaVerify = (token: string) => {
    setCaptchaToken(token)
    form.setValue("hcaptchaToken", token)
    form.clearErrors("hcaptchaToken")
  }

  const handleCaptchaExpire = () => {
    setCaptchaToken("")
    form.setValue("hcaptchaToken", "")
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link href="/" className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4">
            <ArrowLeft className="h-4 w-4 mr-1" />
            Kembali ke Beranda
          </Link>
          
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
              <MessageSquare className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Pengaduan Masyarakat</h1>
              <p className="text-gray-600">Sampaikan keluhan atau saran terkait pelayanan publik</p>
            </div>
          </div>
        </div>

        {/* Form */}
        <Card>
          <CardHeader>
            <CardTitle>Form Pengaduan Masyarakat</CardTitle>
            <CardDescription>
              Sampaikan keluhan, saran, atau masukan Anda terkait pelayanan publik. 
              Pengaduan Anda akan ditindaklanjuti dengan baik.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {/* Jenis Pengaduan */}
                <FormField
                  control={form.control}
                  name="jenisPengaduan"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Jenis Pengaduan *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih jenis pengaduan" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {jenisPengaduanOptions.map((jenis) => (
                            <SelectItem key={jenis} value={jenis}>
                              {jenis}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Lokasi */}
                <FormField
                  control={form.control}
                  name="lokasi"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Lokasi Kejadian *</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="Alamat lengkap atau lokasi spesifik kejadian"
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        Sebutkan alamat lengkap atau lokasi spesifik dimana masalah terjadi.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Kronologi */}
                <FormField
                  control={form.control}
                  name="kronologi"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Detail Pengaduan *</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Jelaskan secara detail masalah yang Anda hadapi, kapan kejadian berlangsung, siapa yang terlibat, dan dampaknya..."
                          className="min-h-[150px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Minimal 50 karakter. Semakin detail informasi yang diberikan, semakin mudah proses penanganan.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Multiple File Upload */}
                <div className="space-y-2">
                  <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Bukti Pendukung (Opsional)
                  </label>
                  <MultipleFileUpload
                    selectedFiles={selectedFiles}
                    onFilesChange={setSelectedFiles}
                    maxFiles={5}
                    maxSize={10 * 1024 * 1024} // 10MB
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                  />
                  <p className="text-sm text-muted-foreground">
                    Upload dokumen, foto, atau file lain yang mendukung pengaduan Anda.
                  </p>
                </div>

                {/* Kontak Opsional */}
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h3 className="text-sm font-medium text-green-800 mb-2">
                    Kontak Pengadu (Opsional)
                  </h3>
                  <p className="text-xs text-green-700 mb-4">
                    Jika Anda ingin mendapat update terkait penanganan pengaduan, silakan isi kontak di bawah. 
                    Informasi ini akan dijaga kerahasiaannya.
                  </p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="kontakEmail"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email</FormLabel>
                          <FormControl>
                            <Input placeholder="<EMAIL>" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="kontakHp"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>No. HP</FormLabel>
                          <FormControl>
                            <Input placeholder="08xxxxxxxxxx" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* hCaptcha */}
                <FormField
                  control={form.control}
                  name="hcaptchaToken"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Verifikasi Captcha *</FormLabel>
                      <FormControl>
                        <div>
                          <div className={`flex justify-center p-4 rounded-lg border-2 ${
                            captchaToken ? 'border-green-200 bg-green-50' : 'border-orange-200 bg-orange-50'
                          }`}>
                            <HCaptcha
                              ref={captchaRef}
                              sitekey={process.env.NEXT_PUBLIC_HCAPTCHA_SITE_KEY || "10000000-ffff-ffff-ffff-000000000001"}
                              onVerify={handleCaptchaVerify}
                              onExpire={handleCaptchaExpire}
                              onError={() => {
                                toast.error("Terjadi kesalahan pada captcha")
                                setCaptchaToken("")
                                form.setValue("hcaptchaToken", "")
                              }}
                            />
                          </div>
                          {!captchaToken && (
                            <p className="text-sm text-orange-600 mt-2 text-center">
                              ⚠️ Selesaikan captcha untuk mengaktifkan tombol kirim
                            </p>
                          )}
                          {captchaToken && (
                            <p className="text-sm text-green-600 mt-2 text-center">
                              ✅ Captcha berhasil diverifikasi
                            </p>
                          )}
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Submit Button */}
                <div className="flex justify-end space-x-4">
                  <Link href="/">
                    <Button type="button" variant="outline">
                      Batal
                    </Button>
                  </Link>
                  <Button type="submit" disabled={isSubmitting || !captchaToken}>
                    {isSubmitting ? "Mengirim..." : (!captchaToken ? "Selesaikan Captcha untuk Melanjutkan" : "Kirim Pengaduan")}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* Info */}
        <div className="mt-8 bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-green-800 mb-2">
            Komitmen Pelayanan
          </h3>
          <ul className="text-xs text-green-700 space-y-1">
            <li>• Pengaduan akan ditanggapi dalam 1x24 jam</li>
            <li>• Proses penanganan maksimal 14 hari kerja</li>
            <li>• Update progress akan diberikan secara berkala</li>
            <li>• Identitas pengadu dijaga kerahasiaannya</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
