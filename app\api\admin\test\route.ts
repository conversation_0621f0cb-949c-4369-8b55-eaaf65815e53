import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Test simple query
    const count = await prisma.laporan.count()
    
    return NextResponse.json({
      success: true,
      data: {
        count,
        user: session.user.name, // Fixed: use 'name' instead of 'nama'
        role: session.user.role
      }
    })

  } catch (error) {
    console.error("Test API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
