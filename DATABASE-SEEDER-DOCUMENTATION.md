# 🌱 Database Seeder Documentation - Laporan Test Data

## 🎯 **Purpose**
Membuat data seed untuk testing sistem RBAC (Role-Based Access Control) dengan berbagai jenis laporan dan status yang representatif.

## ✅ **Seeder Scripts Created**

### **1. 📝 Basic Seeder (`scripts/seed-laporan.js`)**
- **Purpose**: Initial seed dengan data dasar untuk setiap jenis laporan
- **Records**: 12 laporan (4 WBS, 4 COI, 4 PENGADUAN)
- **Features**: Realistic data dengan kronologi lengkap dan kontak pelapor

### **2. 📊 Extended Seeder (`scripts/seed-laporan-extended.js`)**
- **Purpose**: Additional data untuk comprehensive RBAC testing
- **Records**: 15 laporan tambahan
- **Features**: More diverse scenarios dan edge cases

## 📊 **Final Database State**

### **Total Records: 30 Laporan**

#### **By Jenis (Report Type):**
```
WBS (Whistleblowing):        12 records (40%)
COI (Conflict of Interest):   9 records (30%)
PENGADUAN (Public Complaint): 9 records (30%)
```

#### **By Status:**
```
BARU (New):        12 records (40%)
DIPROSES (Processing): 8 records (27%)
SELESAI (Completed):  10 records (33%)
```

#### **RBAC Testing Distribution:**
```
Super Admin access:  30 records (100% - all types)
Admin WBS access:    12 records (40% - WBS only, list view)
Admin COI access:     9 records (30% - COI only, full access)
Admin PM access:      9 records (30% - PENGADUAN only, full access)
```

## 🔧 **Seeder Features**

### **1. 📋 Realistic Data Structure**

#### **WBS Reports Include:**
- **Kronologi**: Detailed whistleblowing scenarios
- **Kategori**: Various corruption types (Penyalahgunaan Anggaran, Korupsi Pengadaan, etc.)
- **Pejabat Dilaporkan**: Specific official positions
- **Lokasi**: Real Jakarta government offices
- **Bukti URL**: Mock evidence file paths

#### **COI Reports Include:**
- **Kronologi**: Conflict of interest scenarios
- **Jenis Benturan**: Types of conflicts (Finansial, Keluarga, Bisnis, Pribadi)
- **Pejabat Dilaporkan**: Officials with potential conflicts
- **Lokasi**: Various government agencies
- **Bukti URL**: Supporting evidence files

#### **PENGADUAN Reports Include:**
- **Kronologi**: Public service complaints
- **Jenis Pengaduan**: Complaint categories (Pelayanan Publik, Diskriminasi, etc.)
- **Lokasi**: Public service locations
- **Bukti URL**: Complaint evidence

### **2. 📞 Contact Information**
- **Email**: Realistic email addresses for reporters
- **Phone**: Indonesian mobile number format
- **Privacy**: Anonymous reporting options

### **3. 📅 Temporal Distribution**
- **Random Dates**: Created within last 30-60 days
- **Realistic Timeline**: Processing times vary by status
- **Updated Timestamps**: Proper audit trail

## 🎯 **RBAC Testing Scenarios**

### **1. 👑 Super Admin Testing**
```sql
-- Can access all 30 records
SELECT * FROM laporan; -- Returns all records
```
- ✅ **View All**: Can see WBS, COI, and PENGADUAN
- ✅ **Export All**: Can export all report types
- ✅ **Full Details**: Access to all fields and contact info

### **2. 📝 Admin WBS Testing**
```sql
-- Can only access WBS records (12 records)
SELECT * FROM laporan WHERE jenis = 'WBS';
```
- ✅ **View WBS List**: Can see 12 WBS records
- ❌ **No Detail Access**: Cannot view full details
- ❌ **No Export**: Export menu hidden
- ❌ **No COI/PM**: Cannot see other report types

### **3. 📋 Admin COI Testing**
```sql
-- Can only access COI records (9 records)
SELECT * FROM laporan WHERE jenis = 'COI';
```
- ✅ **View COI List**: Can see 9 COI records
- ✅ **Full Details**: Can view complete COI information
- ✅ **Export COI**: Can export COI reports only
- ❌ **No WBS/PM**: Cannot see other report types

### **4. 📞 Admin PM Testing**
```sql
-- Can only access PENGADUAN records (9 records)
SELECT * FROM laporan WHERE jenis = 'PENGADUAN';
```
- ✅ **View PM List**: Can see 9 PENGADUAN records
- ✅ **Full Details**: Can view complete complaint information
- ✅ **Export PM**: Can export PENGADUAN reports only
- ❌ **No WBS/COI**: Cannot see other report types

## 🧪 **Testing Use Cases**

### **1. 🔍 List View Testing**
- **Super Admin**: Should see all 30 records
- **Admin WBS**: Should see 12 WBS records (no detail buttons)
- **Admin COI**: Should see 9 COI records (with detail buttons)
- **Admin PM**: Should see 9 PENGADUAN records (with detail buttons)

### **2. 📄 Detail View Testing**
- **Super Admin**: Can access any detail page
- **Admin WBS**: Redirected or access denied for detail pages
- **Admin COI**: Can access COI details, denied for others
- **Admin PM**: Can access PM details, denied for others

### **3. 📊 Export Testing**
- **Super Admin**: Can export all types, sees all filter options
- **Admin WBS**: Export menu hidden, redirected if direct access
- **Admin COI**: Can export COI only, filter shows COI only
- **Admin PM**: Can export PM only, filter shows PENGADUAN only

### **4. 🔒 Security Testing**
- **Direct URL Access**: Unauthorized users get 403/redirect
- **API Calls**: RBAC filters applied to all queries
- **Data Leakage**: Users only see authorized data

## 📋 **Sample Data Examples**

### **WBS Report Example:**
```json
{
  "id": 1,
  "jenis": "WBS",
  "status": "BARU",
  "kronologi": "Ditemukan adanya praktik whistleblowing terkait penyalahgunaan anggaran proyek infrastruktur...",
  "lokasi": "Dinas Pekerjaan Umum, Jakarta Pusat",
  "kategori": "Penyalahgunaan Anggaran",
  "pejabatDilaporkan": "Kepala Dinas PU Jakarta Pusat",
  "buktiUrl": "/uploads/bukti-wbs-001.pdf",
  "kontakPelapor": {
    "email": "<EMAIL>",
    "noHp": "081234567890"
  }
}
```

### **COI Report Example:**
```json
{
  "id": 22,
  "jenis": "COI",
  "status": "BARU",
  "kronologi": "Ditemukan konflik kepentingan dimana Kepala Bagian Pengadaan memiliki saham...",
  "lokasi": "Dinas Pendidikan Jakarta Barat",
  "jenisBenturan": "Kepentingan Finansial",
  "pejabatDilaporkan": "Kepala Bagian Pengadaan Disdik Jakbar",
  "buktiUrl": "/uploads/bukti-coi-001.pdf"
}
```

### **PENGADUAN Report Example:**
```json
{
  "id": 26,
  "jenis": "PENGADUAN",
  "status": "BARU",
  "kronologi": "Masyarakat mengeluhkan pelayanan yang buruk di kantor kelurahan...",
  "lokasi": "Kelurahan Menteng, Jakarta Pusat",
  "jenisPengaduan": "Pelayanan Publik",
  "buktiUrl": "/uploads/bukti-pengaduan-001.pdf"
}
```

## 🚀 **Running the Seeders**

### **1. Basic Seeder:**
```bash
node scripts/seed-laporan.js
```
- Creates 12 initial records
- Clears existing data first
- Provides summary statistics

### **2. Extended Seeder:**
```bash
node scripts/seed-laporan-extended.js
```
- Adds 15 additional records
- Preserves existing data
- Comprehensive RBAC testing data

### **3. Complete Setup:**
```bash
# Run both seeders for full test data
node scripts/seed-laporan.js
node scripts/seed-laporan-extended.js
```

## 📊 **Verification Queries**

### **Check Total Records:**
```sql
SELECT COUNT(*) as total FROM laporan;
-- Expected: 30
```

### **Check Distribution by Type:**
```sql
SELECT jenis, COUNT(*) as count 
FROM laporan 
GROUP BY jenis;
-- Expected: WBS=12, COI=9, PENGADUAN=9
```

### **Check Distribution by Status:**
```sql
SELECT status, COUNT(*) as count 
FROM laporan 
GROUP BY status;
-- Expected: BARU=12, DIPROSES=8, SELESAI=10
```

### **RBAC Filter Testing:**
```sql
-- Super Admin (all records)
SELECT COUNT(*) FROM laporan; -- 30

-- Admin WBS (WBS only)
SELECT COUNT(*) FROM laporan WHERE jenis = 'WBS'; -- 12

-- Admin COI (COI only)
SELECT COUNT(*) FROM laporan WHERE jenis = 'COI'; -- 9

-- Admin PM (PENGADUAN only)
SELECT COUNT(*) FROM laporan WHERE jenis = 'PENGADUAN'; -- 9
```

## 🎯 **Benefits**

### **Testing Benefits:**
- ✅ **Comprehensive Coverage**: All report types and statuses
- ✅ **RBAC Validation**: Perfect for testing role-based access
- ✅ **Realistic Data**: Authentic scenarios for better testing
- ✅ **Edge Cases**: Various combinations for thorough testing

### **Development Benefits:**
- ✅ **Quick Setup**: Instant test data for development
- ✅ **Consistent Data**: Same data across environments
- ✅ **Demo Ready**: Professional data for demonstrations
- ✅ **Performance Testing**: Sufficient volume for performance tests

### **Quality Assurance:**
- ✅ **Automated Testing**: Consistent data for automated tests
- ✅ **Manual Testing**: Rich scenarios for manual testing
- ✅ **Security Testing**: Perfect for RBAC security validation
- ✅ **User Acceptance**: Realistic data for UAT

---

**🎉 Database seeder successfully created dengan 30 laporan untuk comprehensive RBAC testing!**
