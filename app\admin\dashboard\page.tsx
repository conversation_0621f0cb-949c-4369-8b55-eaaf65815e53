"use client"

import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  Calendar,
  Users,
  Shield,
  Eye,
  EyeOff
} from "lucide-react"
import Link from "next/link"
import { StatusLaporanEnum } from "@/lib/api-response"

interface DashboardStats {
  totalLaporan: number
  laporanBulanIni: number
  laporanMingguIni: number
  laporanHariIni: number
  jenisStats: Record<string, number>
  statusStats: Record<string, number>
  recentLaporan: Array<{
    id: number
    jenis: string
    status: string
    createdAt: string
    kronologi: string
    lokasi?: string
  }>
  userRole?: string
  accessibleTypes?: string[]
  userPermissions?: {
    canViewWBSDetail: boolean
    canViewCOIDetail: boolean
    canViewPMDetail: boolean
  }
}

export default function DashboardPage() {
  const { data: session } = useSession()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      const response = await fetch("/api/dashboard/stats")
      if (response.ok) {
        const result = await response.json()

        if (result.success && result.data) {
          setStats(result.data) // Extract data from API response
        } else {
          console.error("API returned error:", result.message)
        }
      } else {
        console.error("API request failed:", response.status, response.statusText)
      }
    } catch (error) {
      console.error("Error fetching stats:", error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case StatusLaporanEnum.BARU:
        return <Badge variant="destructive">Baru</Badge>
      case StatusLaporanEnum.DIPROSES:
        return <Badge variant="default">Diproses</Badge>
      case StatusLaporanEnum.SELESAI:
        return <Badge variant="secondary">Selesai</Badge>
      default:
        return <Badge variant="outline">{status || "-"}</Badge>
    }
  }

  const getJenisBadge = (jenis: string) => {
    switch (jenis) {
      case "WBS":
        return <Badge className="bg-blue-100 text-blue-800">WBS</Badge>
      case "COI":
        return <Badge className="bg-orange-100 text-orange-800">COI</Badge>
      case "PENGADUAN":
        return <Badge className="bg-green-100 text-green-800">Pengaduan</Badge>
      default:
        return <Badge variant="outline">{jenis || "-"}</Badge>
    }
  }

  // Check if user can view details based on RBAC
  const canViewDetails = stats?.userPermissions?.canViewWBSDetail ||
                        stats?.userPermissions?.canViewCOIDetail ||
                        stats?.userPermissions?.canViewPMDetail

  // Function to render kronologi based on user permissions
  const renderKronologi = (kronologi: string, jenis: string) => {
    const canViewThisType =
      (jenis === "WBS" && stats?.userPermissions?.canViewWBSDetail) ||
      (jenis === "COI" && stats?.userPermissions?.canViewCOIDetail) ||
      (jenis === "PENGADUAN" && stats?.userPermissions?.canViewPMDetail)

    if (canViewThisType && !kronologi.includes("[Detail hanya dapat dilihat")) {
      return (
        <p className="text-sm text-gray-600 line-clamp-2">
          {kronologi}
        </p>
      )
    } else {
      return (
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <EyeOff className="h-4 w-4" />
          <span className="italic">
            {kronologi.includes("[Detail hanya dapat dilihat")
              ? kronologi
              : "Detail laporan hanya dapat dilihat oleh pengguna yang berwenang"
            }
          </span>
        </div>
      )
    }
  }

  // Get role display name
  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case "SUPER_ADMIN": return "Super Admin"
      case "ADMIN": return "Admin"
      case "ADMIN_WBS": return "Admin WBS"
      case "ADMIN_COI": return "Admin COI"
      case "ADMIN_PM": return "Admin PM"
      default: return role
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Gagal memuat data dashboard</p>
        <Button onClick={fetchStats} className="mt-4">
          Coba Lagi
        </Button>
      </div>
    )
  }

  // Ensure stats have default values to prevent undefined errors
  const safeStats = {
    totalLaporan: stats?.totalLaporan ?? 0,
    laporanBulanIni: stats?.laporanBulanIni ?? 0,
    laporanMingguIni: stats?.laporanMingguIni ?? 0,
    laporanHariIni: stats?.laporanHariIni ?? 0,
    jenisStats: stats?.jenisStats || { WBS: 0, COI: 0, PENGADUAN: 0 },
    statusStats: stats?.statusStats || { BARU: 0, DIPROSES: 0, SELESAI: 0 },
    recentLaporan: stats?.recentLaporan || [],
    userRole: stats?.userRole,
    accessibleTypes: stats?.accessibleTypes || [],
    userPermissions: stats?.userPermissions || {
      canViewWBSDetail: false,
      canViewCOIDetail: false,
      canViewPMDetail: false
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Overview sistem pelaporan WBS, COI, dan Pengaduan</p>
        </div>
        <div className="mt-4 sm:mt-0 flex flex-col sm:items-end">
          <div className="flex items-center gap-2">
            <Shield className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">
              {getRoleDisplayName(stats?.userRole || session?.user?.role || "")}
            </span>
          </div>
          {stats?.accessibleTypes && stats.accessibleTypes.length > 0 && (
            <div className="flex items-center gap-2 mt-1">
              <Eye className="h-4 w-4 text-gray-400" />
              <span className="text-xs text-gray-500">
                Akses: {stats.accessibleTypes.join(", ")}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Laporan</p>
                <p className="text-2xl font-bold text-gray-900">{safeStats.totalLaporan}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Calendar className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Bulan Ini</p>
                <p className="text-2xl font-bold text-gray-900">{safeStats.laporanBulanIni}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Minggu Ini</p>
                <p className="text-2xl font-bold text-gray-900">{safeStats.laporanMingguIni}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Clock className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Hari Ini</p>
                <p className="text-2xl font-bold text-gray-900">{safeStats.laporanHariIni}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Laporan by Jenis */}
        <Card>
          <CardHeader>
            <CardTitle>Laporan by Jenis</CardTitle>
            <CardDescription>Distribusi laporan berdasarkan jenis</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Only show accessible report types */}
              {(!stats?.accessibleTypes || stats.accessibleTypes.includes("WBS")) && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                    <span className="text-sm">WBS</span>
                  </div>
                  <span className="font-semibold">{safeStats.jenisStats["WBS"] ?? 0}</span>
                </div>
              )}
              {(!stats?.accessibleTypes || stats.accessibleTypes.includes("COI")) && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                    <span className="text-sm">COI</span>
                  </div>
                  <span className="font-semibold">{safeStats.jenisStats["COI"] ?? 0}</span>
                </div>
              )}
              {(!stats?.accessibleTypes || stats.accessibleTypes.includes("PENGADUAN")) && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-sm">Pengaduan</span>
                  </div>
                  <span className="font-semibold">{safeStats.jenisStats["PENGADUAN"] ?? 0}</span>
                </div>
              )}

              {/* Show message if no accessible types */}
              {stats?.accessibleTypes && stats.accessibleTypes.length === 0 && (
                <div className="text-center py-4 text-gray-500">
                  <EyeOff className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-sm">Tidak ada akses ke jenis laporan</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Laporan by Status */}
        <Card>
          <CardHeader>
            <CardTitle>Laporan by Status</CardTitle>
            <CardDescription>Distribusi laporan berdasarkan status</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <AlertCircle className="w-4 h-4 text-red-500 mr-2" />
                  <span className="text-sm">Baru</span>
                </div>
                <span className="font-semibold">{safeStats.statusStats["BARU"] ?? 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Clock className="w-4 h-4 text-blue-500 mr-2" />
                  <span className="text-sm">Diproses</span>
                </div>
                <span className="font-semibold">{safeStats.statusStats["DIPROSES"] ?? 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                  <span className="text-sm">Selesai</span>
                </div>
                <span className="font-semibold">{safeStats.statusStats["SELESAI"] ?? 0}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Laporan */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Laporan Terbaru</CardTitle>
              <CardDescription>5 laporan terbaru yang masuk</CardDescription>
            </div>
            <Link href="/admin/laporan">
              <Button variant="outline" size="sm">
                Lihat Semua
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {safeStats.recentLaporan.length === 0 ? (
              <p className="text-gray-500 text-center py-4">Belum ada laporan</p>
            ) : (
              safeStats.recentLaporan.map((laporan) => (
                <div key={laporan.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      {getJenisBadge(laporan.jenis)}
                      {getStatusBadge(laporan.status)}
                      {!canViewDetails && (
                        <Badge variant="outline" className="text-xs">
                          <Shield className="h-3 w-3 mr-1" />
                          Terbatas
                        </Badge>
                      )}
                    </div>
                    {renderKronologi(laporan.kronologi, laporan.jenis)}
                    {laporan.lokasi && (
                      <p className="text-xs text-gray-500 mt-1">
                        Lokasi: {laporan.lokasi}
                      </p>
                    )}
                    <p className="text-xs text-gray-400 mt-1">
                      {new Date(laporan.createdAt).toLocaleDateString('id-ID', {
                        day: 'numeric',
                        month: 'long',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                  </div>
                  {(() => {
                    const canViewThisDetail =
                      (laporan.jenis === "WBS" && stats?.userPermissions?.canViewWBSDetail) ||
                      (laporan.jenis === "COI" && stats?.userPermissions?.canViewCOIDetail) ||
                      (laporan.jenis === "PENGADUAN" && stats?.userPermissions?.canViewPMDetail)

                    return (
                      <Link href={`/admin/laporan/${laporan.id}`}>
                        <Button variant="ghost" size="sm">
                          {canViewThisDetail ? (
                            <>
                              <Eye className="h-4 w-4 mr-1" />
                              Detail
                            </>
                          ) : (
                            <>
                              <Shield className="h-4 w-4 mr-1" />
                              Lihat
                            </>
                          )}
                        </Button>
                      </Link>
                    )
                  })()}
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
