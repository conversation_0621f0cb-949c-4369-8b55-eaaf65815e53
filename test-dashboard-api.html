<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { border: 1px solid #ddd; padding: 20px; margin: 10px 0; border-radius: 8px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .loading { background-color: #fff3cd; border-color: #ffeaa7; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Dashboard API Test</h1>
        
        <div class="card">
            <h3>Test Dashboard Stats API</h3>
            <button onclick="testDashboardAPI()">Test API</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        async function testDashboardAPI() {
            const resultsDiv = document.getElementById('results');
            
            // Show loading
            resultsDiv.innerHTML = '<div class="card loading"><h4>Loading...</h4><p>Testing dashboard API...</p></div>';
            
            try {
                console.log('Fetching dashboard stats...');
                const response = await fetch('/api/dashboard/stats', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include' // Include cookies for session
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                const data = await response.json();
                console.log('Response data:', data);
                
                if (response.ok) {
                    // Success
                    resultsDiv.innerHTML = `
                        <div class="card success">
                            <h4>✅ API Success (${response.status})</h4>
                            <p><strong>Message:</strong> ${data.message || 'No message'}</p>
                            <p><strong>Success:</strong> ${data.success}</p>
                            
                            ${data.data ? `
                                <h5>Dashboard Data:</h5>
                                <ul>
                                    <li><strong>Total Laporan:</strong> ${data.data.totalLaporan || 0}</li>
                                    <li><strong>Bulan Ini:</strong> ${data.data.laporanBulanIni || 0}</li>
                                    <li><strong>Minggu Ini:</strong> ${data.data.laporanMingguIni || 0}</li>
                                    <li><strong>Hari Ini:</strong> ${data.data.laporanHariIni || 0}</li>
                                    <li><strong>User Role:</strong> ${data.data.userRole || 'Unknown'}</li>
                                    <li><strong>Accessible Types:</strong> ${data.data.accessibleTypes ? data.data.accessibleTypes.join(', ') : 'None'}</li>
                                </ul>
                                
                                <h5>Jenis Stats:</h5>
                                <ul>
                                    <li><strong>WBS:</strong> ${data.data.jenisStats?.WBS || 0}</li>
                                    <li><strong>COI:</strong> ${data.data.jenisStats?.COI || 0}</li>
                                    <li><strong>PENGADUAN:</strong> ${data.data.jenisStats?.PENGADUAN || 0}</li>
                                </ul>
                                
                                <h5>Status Stats:</h5>
                                <ul>
                                    <li><strong>BARU:</strong> ${data.data.statusStats?.BARU || 0}</li>
                                    <li><strong>DIPROSES:</strong> ${data.data.statusStats?.DIPROSES || 0}</li>
                                    <li><strong>SELESAI:</strong> ${data.data.statusStats?.SELESAI || 0}</li>
                                </ul>
                                
                                <h5>Recent Reports:</h5>
                                <p><strong>Count:</strong> ${data.data.recentLaporan ? data.data.recentLaporan.length : 0}</p>
                            ` : '<p><strong>No data received</strong></p>'}
                            
                            <h5>Full Response:</h5>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    // Error response
                    resultsDiv.innerHTML = `
                        <div class="card error">
                            <h4>❌ API Error (${response.status})</h4>
                            <p><strong>Message:</strong> ${data.message || 'No message'}</p>
                            <p><strong>Error:</strong> ${data.error || 'Unknown error'}</p>
                            <h5>Full Response:</h5>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
                
            } catch (error) {
                console.error('Fetch error:', error);
                resultsDiv.innerHTML = `
                    <div class="card error">
                        <h4>❌ Network Error</h4>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p>Check console for more details.</p>
                    </div>
                `;
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // Auto-test on page load
        window.addEventListener('load', () => {
            console.log('Page loaded, ready to test API');
        });
    </script>
</body>
</html>
