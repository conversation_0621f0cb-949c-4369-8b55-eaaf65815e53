import { NextRequest, NextResponse } from "next/server"
import { getToken } from "next-auth/jwt"
import { checkAccess, ReportType, Action } from "./rbac"
import { UserRole } from "@prisma/client"

/**
 * RBAC Middleware for protecting routes based on user roles
 */
export async function rbacMiddleware(request: NextRequest) {
  const token = await getToken({ req: request })
  
  if (!token || !token.role) {
    return NextResponse.redirect(new URL("/admin/login", request.url))
  }

  const userRole = token.role as UserRole
  const pathname = request.nextUrl.pathname

  // Check access for specific routes
  if (pathname.startsWith("/admin/laporan/")) {
    return await handleLaporanAccess(request, userRole, pathname)
  }

  // Allow access to other admin routes for all admin users
  return NextResponse.next()
}

/**
 * Handle access control for laporan routes
 */
async function handleLaporanAccess(
  request: NextRequest,
  userRole: UserRole,
  pathname: string
): Promise<NextResponse> {
  
  // Extract report ID from URL if it's a detail page (UUID format: 6 alphanumeric characters)
  const reportIdMatch = pathname.match(/\/admin\/laporan\/([A-Z0-9]{6})/)
  const isDetailPage = !!reportIdMatch
  const reportId = reportIdMatch?.[1]

  if (isDetailPage && reportId) {
    // This is a detail page - check if user can view details
    return await handleDetailPageAccess(request, userRole, reportId)
  }

  // This is the list page - check if user can view any reports
  const canViewAnyReports = 
    checkAccess(userRole, "WBS", "viewList") ||
    checkAccess(userRole, "COI", "viewList") ||
    checkAccess(userRole, "PENGADUAN", "viewList")

  if (!canViewAnyReports) {
    return NextResponse.redirect(new URL("/admin/dashboard?error=access_denied", request.url))
  }

  return NextResponse.next()
}

/**
 * Handle access control for report detail pages
 */
async function handleDetailPageAccess(
  request: NextRequest,
  userRole: UserRole,
  reportId: string
): Promise<NextResponse> {
  
  try {
    // We need to fetch the report to know its type
    // Since we can't import Prisma in middleware, we'll make an API call
    const reportResponse = await fetch(new URL(`/api/laporan/${reportId}`, request.url), {
      headers: {
        'Authorization': `Bearer ${request.headers.get('authorization')}`,
        'Cookie': request.headers.get('cookie') || '',
      },
    })

    if (!reportResponse.ok) {
      return NextResponse.redirect(new URL("/admin/laporan?error=report_not_found", request.url))
    }

    const report = await reportResponse.json()
    const reportType = report.jenis as ReportType

    // Check if user can view details for this report type
    const canViewDetail = checkAccess(userRole, reportType, "viewDetail")

    if (!canViewDetail) {
      // Redirect back to list with access denied message
      return NextResponse.redirect(
        new URL(`/admin/laporan?error=detail_access_denied&type=${reportType}`, request.url)
      )
    }

    return NextResponse.next()

  } catch (error) {
    console.error("Error checking report access:", error)
    return NextResponse.redirect(new URL("/admin/laporan?error=access_check_failed", request.url))
  }
}

/**
 * Check if user has admin access to any admin routes
 */
export function hasAdminAccess(userRole: UserRole | string): boolean {
  const role = typeof userRole === 'string' ? userRole as UserRole : userRole

  // Define admin roles explicitly to avoid enum inconsistencies
  const adminRoles: string[] = [
    'SUPER_ADMIN',
    'ADMIN', // Legacy support
    'ADMIN_WBS',
    'ADMIN_COI',
    'ADMIN_PM',
    'VERIFIKATOR',
    'INVESTIGATOR',
  ]

  return adminRoles.includes(role)
}

/**
 * Get redirect URL based on user role after login
 */
export function getRedirectUrlForRole(userRole: UserRole | string): string {
  const role = typeof userRole === 'string' ? userRole as UserRole : userRole
  
  // All admin roles go to admin dashboard
  if (hasAdminAccess(role)) {
    return "/admin/dashboard"
  }

  // Regular users go to home page
  return "/"
}
