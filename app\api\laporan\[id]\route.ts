import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { StatusLaporan } from "@prisma/client"
import { sendStatusUpdateNotification } from "@/lib/email"
import { isValidUUID6 } from "@/lib/uuid-generator"

/**
 * Handler untuk mengambil detail laporan (GET /api/laporan/[id])
 * @example
 * // Response sukses
 * {
 *   success: true,
 *   data: { ...laporan },
 *   message: "Detail laporan berhasil diambil",
 *   error: null
 * }
 * // Response gagal
 * {
 *   success: false,
 *   data: null,
 *   message: "Laporan tidak ditemukan",
 *   error: "NOT_FOUND"
 * }
 * @param request NextRequest - request dari client
 * @param params Object - parameter route, berisi id laporan
 * @returns NextResponse - detail laporan atau error
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { success: false, data: null, message: "Unauthorized", error: "UNAUTHORIZED" },
        { status: 401 }
      )
    }

    const { id: idParam } = await params
    const id = idParam

    // Validate UUID format
    if (!isValidUUID6(id)) {
      return NextResponse.json(
        { success: false, data: null, message: "ID laporan tidak valid", error: "INVALID_ID" },
        { status: 400 }
      )
    }

    const laporan = await prisma.laporan.findUnique({
      where: { id },
      include: {
        kontakPelapor: true,
      },
    })

    if (!laporan) {
      return NextResponse.json(
        { success: false, data: null, message: "Laporan tidak ditemukan", error: "NOT_FOUND" },
        { status: 404 }
      )
    }

    // Check if user is admin
    const isAdmin = session.user.role === "ADMIN"

    // If not admin, hide sensitive information
    if (!isAdmin) {
      return NextResponse.json({
        success: true,
        data: {
          ...laporan,
          kronologi: "[Detail kronologi hanya dapat dilihat oleh Admin]",
          buktiUrl: laporan.buktiUrl ? "[Bukti tersedia - akses terbatas untuk Admin]" : null,
          kontakPelapor: laporan.kontakPelapor ? {
            ...laporan.kontakPelapor,
            email: "[Email tersembunyi]",
            noHp: "[No HP tersembunyi]"
          } : null
        },
        message: "Detail laporan berhasil diambil",
        error: null
      })
    }

    return NextResponse.json({
      success: true,
      data: laporan,
      message: "Detail laporan berhasil diambil",
      error: null
    })

  } catch (error) {
    console.error("[GET /api/laporan/[id]]", error)
    return NextResponse.json(
      { success: false, data: null, message: "Terjadi kesalahan server", error: "INTERNAL_SERVER_ERROR" },
      { status: 500 }
    )
  }
}

/**
 * Handler untuk mengubah status laporan (PATCH /api/laporan/[id])
 * @example
 * // Response sukses
 * {
 *   success: true,
 *   data: { ...laporan },
 *   message: "Status laporan berhasil diupdate",
 *   error: null
 * }
 * // Response gagal
 * {
 *   success: false,
 *   data: null,
 *   message: "Status tidak valid",
 *   error: "VALIDATION_ERROR"
 * }
 * @param request NextRequest - request dari client, berisi status baru
 * @param params Object - parameter route, berisi id laporan
 * @returns NextResponse - hasil update status atau error
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { success: false, data: null, message: "Unauthorized", error: "UNAUTHORIZED" },
        { status: 401 }
      )
    }

    const { id: idParam } = await params
    const id = idParam

    // Validate UUID format
    if (!isValidUUID6(id)) {
      return NextResponse.json(
        { success: false, data: null, message: "ID laporan tidak valid", error: "INVALID_ID" },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { status } = body

    // Validasi status
    if (!Object.values(StatusLaporan).includes(status)) {
      return NextResponse.json(
        { success: false, data: null, message: "Status tidak valid", error: "VALIDATION_ERROR" },
        { status: 400 }
      )
    }

    // Cek apakah laporan ada
    const existingLaporan = await prisma.laporan.findUnique({
      where: { id },
    })

    if (!existingLaporan) {
      return NextResponse.json(
        { success: false, data: null, message: "Laporan tidak ditemukan", error: "NOT_FOUND" },
        { status: 404 }
      )
    }

    // Update status laporan
    const updatedLaporan = await prisma.laporan.update({
      where: { id },
      data: {
        status: status as StatusLaporan,
        updatedAt: new Date(),
      },
      include: {
        kontakPelapor: true,
      },
    })

    // Send email notification if email is available and status changed
    if (updatedLaporan.kontakPelapor?.email && existingLaporan.status !== status) {
      try {
        await sendStatusUpdateNotification(
          updatedLaporan.kontakPelapor.email,
          updatedLaporan.id,
          updatedLaporan.jenis,
          existingLaporan.status,
          status
        )
        console.log(`Status update email sent to ${updatedLaporan.kontakPelapor.email} for laporan #${updatedLaporan.id}`)
      } catch (error) {
        console.error('Failed to send status update email:', error)
        // Don't fail the request if email fails
      }
    }

    return NextResponse.json({
      success: true,
      data: updatedLaporan,
      message: "Status laporan berhasil diupdate",
      error: null
    })

  } catch (error) {
    console.error("[PATCH /api/laporan/[id]]", error)
    return NextResponse.json(
      { success: false, data: null, message: "Terjadi kesalahan server", error: "INTERNAL_SERVER_ERROR" },
      { status: 500 }
    )
  }
}

/**
 * Handler untuk menghapus laporan (DELETE /api/laporan/[id])
 * @example
 * // Response sukses
 * {
 *   success: true,
 *   data: null,
 *   message: "Laporan berhasil dihapus",
 *   error: null
 * }
 * // Response gagal
 * {
 *   success: false,
 *   data: null,
 *   message: "Laporan tidak ditemukan",
 *   error: "NOT_FOUND"
 * }
 * @param request NextRequest - request dari client
 * @param params Object - parameter route, berisi id laporan
 * @returns NextResponse - hasil hapus laporan atau error
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, data: null, message: "Unauthorized", error: "UNAUTHORIZED" },
        { status: 401 }
      )
    }

    const { id: idParam } = await params
    const id = idParam

    // Validate UUID format
    if (!isValidUUID6(id)) {
      return NextResponse.json(
        { success: false, data: null, message: "ID laporan tidak valid", error: "INVALID_ID" },
        { status: 400 }
      )
    }

    // Cek apakah laporan ada
    const existingLaporan = await prisma.laporan.findUnique({
      where: { id },
    })

    if (!existingLaporan) {
      return NextResponse.json(
        { success: false, data: null, message: "Laporan tidak ditemukan", error: "NOT_FOUND" },
        { status: 404 }
      )
    }

    // Hapus laporan (akan otomatis menghapus kontak pelapor karena cascade)
    await prisma.laporan.delete({
      where: { id },
    })

    return NextResponse.json({
      success: true,
      data: null,
      message: "Laporan berhasil dihapus",
      error: null
    })

  } catch (error) {
    console.error("[DELETE /api/laporan/[id]]", error)
    return NextResponse.json(
      { success: false, data: null, message: "Terjadi kesalahan server", error: "INTERNAL_SERVER_ERROR" },
      { status: 500 }
    )
  }
}
